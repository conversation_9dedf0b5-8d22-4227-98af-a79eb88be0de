/**
 * Smart Auth Frontend JavaScript
 *
 * @package SmartAuth
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    /**
     * Smart Auth main object
     */
    window.SmartAuth = {
        
        /**
         * Initialize Smart Auth
         */
        init: function() {
            this.bindEvents();
            this.initFirebase();
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            $(document).on('click', '.smart-auth-google-button', this.handleGoogleAuth);
            $(document).on('click', '.smart-auth-facebook-button', this.handleFacebookAuth);
            $(document).on('click', '.smart-auth-apple-button', this.handleAppleAuth);
            $(document).on('click', '.smart-auth-phone-button', this.handlePhoneAuth);
            $(document).on('submit', '.smart-auth-otp-form', this.handleOTPSubmit);
            $(document).on('click', '.smart-auth-resend-otp', this.handleResendOTP);
        },
        
        /**
         * Initialize Firebase
         */
        initFirebase: function() {
            if (typeof firebase === 'undefined' || !smartAuth.firebaseConfig.apiKey) {
                console.warn('Firebase not configured or loaded');
                return;
            }
            
            // Initialize Firebase if not already initialized
            if (!firebase.apps.length) {
                firebase.initializeApp(smartAuth.firebaseConfig);
            }
            
            this.auth = firebase.auth();
        },
        
        /**
         * Handle Google authentication
         */
        handleGoogleAuth: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            SmartAuth.showLoading($button);
            
            if (!SmartAuth.auth) {
                SmartAuth.showError(smartAuth.strings.error);
                SmartAuth.hideLoading($button);
                return;
            }
            
            var provider = new firebase.auth.GoogleAuthProvider();
            provider.addScope('email');
            provider.addScope('profile');
            
            SmartAuth.auth.signInWithPopup(provider)
                .then(function(result) {
                    return result.user.getIdToken();
                })
                .then(function(idToken) {
                    SmartAuth.verifyFirebaseToken(idToken, 'google');
                })
                .catch(function(error) {
                    console.error('Google auth error:', error);
                    SmartAuth.showError(error.message || smartAuth.strings.error);
                    SmartAuth.hideLoading($button);
                });
        },
        
        /**
         * Handle Facebook authentication
         */
        handleFacebookAuth: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            SmartAuth.showLoading($button);
            
            if (!SmartAuth.auth) {
                SmartAuth.showError(smartAuth.strings.error);
                SmartAuth.hideLoading($button);
                return;
            }
            
            var provider = new firebase.auth.FacebookAuthProvider();
            provider.addScope('email');
            
            SmartAuth.auth.signInWithPopup(provider)
                .then(function(result) {
                    return result.user.getIdToken();
                })
                .then(function(idToken) {
                    SmartAuth.verifyFirebaseToken(idToken, 'facebook');
                })
                .catch(function(error) {
                    console.error('Facebook auth error:', error);
                    SmartAuth.showError(error.message || smartAuth.strings.error);
                    SmartAuth.hideLoading($button);
                });
        },
        
        /**
         * Handle Apple authentication
         */
        handleAppleAuth: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            SmartAuth.showLoading($button);
            
            if (!SmartAuth.auth) {
                SmartAuth.showError(smartAuth.strings.error);
                SmartAuth.hideLoading($button);
                return;
            }
            
            var provider = new firebase.auth.OAuthProvider('apple.com');
            provider.addScope('email');
            provider.addScope('name');
            
            SmartAuth.auth.signInWithPopup(provider)
                .then(function(result) {
                    return result.user.getIdToken();
                })
                .then(function(idToken) {
                    SmartAuth.verifyFirebaseToken(idToken, 'apple');
                })
                .catch(function(error) {
                    console.error('Apple auth error:', error);
                    SmartAuth.showError(error.message || smartAuth.strings.error);
                    SmartAuth.hideLoading($button);
                });
        },
        
        /**
         * Handle phone authentication
         */
        handlePhoneAuth: function(e) {
            e.preventDefault();
            
            var $form = $(this).closest('.smart-auth-form');
            var phoneNumber = $form.find('.smart-auth-phone-input').val();
            
            if (!phoneNumber) {
                SmartAuth.showError(smartAuth.strings.invalidPhone);
                return;
            }
            
            var $button = $(this);
            SmartAuth.showLoading($button);
            
            SmartAuth.sendPhoneOTP(phoneNumber, $button);
        },
        
        /**
         * Send phone OTP
         */
        sendPhoneOTP: function(phoneNumber, $button) {
            $.ajax({
                url: smartAuth.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'smart_auth_send_otp',
                    phone_number: phoneNumber,
                    nonce: smartAuth.nonce
                },
                success: function(response) {
                    if (response.success) {
                        SmartAuth.showOTPForm(phoneNumber, response.data.session_info);
                        SmartAuth.showSuccess(smartAuth.strings.otpSent);
                    } else {
                        SmartAuth.showError(response.data.message || smartAuth.strings.error);
                    }
                },
                error: function() {
                    SmartAuth.showError(smartAuth.strings.error);
                },
                complete: function() {
                    SmartAuth.hideLoading($button);
                }
            });
        },
        
        /**
         * Handle OTP form submit
         */
        handleOTPSubmit: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var phoneNumber = $form.find('.smart-auth-phone-number').val();
            var otpCode = $form.find('.smart-auth-otp-code').val();
            var sessionInfo = $form.find('.smart-auth-session-info').val();
            
            if (!otpCode) {
                SmartAuth.showError(smartAuth.strings.otpInvalid);
                return;
            }
            
            var $button = $form.find('.smart-auth-verify-otp');
            SmartAuth.showLoading($button);
            
            SmartAuth.verifyPhoneOTP(phoneNumber, otpCode, sessionInfo, $button);
        },
        
        /**
         * Verify phone OTP
         */
        verifyPhoneOTP: function(phoneNumber, otpCode, sessionInfo, $button) {
            $.ajax({
                url: smartAuth.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'smart_auth_verify_otp',
                    phone_number: phoneNumber,
                    otp_code: otpCode,
                    session_info: sessionInfo,
                    nonce: smartAuth.nonce
                },
                success: function(response) {
                    if (response.success) {
                        SmartAuth.handleAuthSuccess(response.data);
                    } else {
                        SmartAuth.showError(response.data.message || smartAuth.strings.otpInvalid);
                    }
                },
                error: function() {
                    SmartAuth.showError(smartAuth.strings.error);
                },
                complete: function() {
                    SmartAuth.hideLoading($button);
                }
            });
        },
        
        /**
         * Verify Firebase token
         */
        verifyFirebaseToken: function(idToken, provider) {
            $.ajax({
                url: smartAuth.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'smart_auth_firebase_verify',
                    id_token: idToken,
                    provider: provider,
                    nonce: smartAuth.nonce
                },
                success: function(response) {
                    if (response.success) {
                        SmartAuth.handleAuthSuccess(response.data);
                    } else {
                        SmartAuth.showError(response.data.message || smartAuth.strings.error);
                    }
                },
                error: function() {
                    SmartAuth.showError(smartAuth.strings.error);
                }
            });
        },
        
        /**
         * Handle authentication success
         */
        handleAuthSuccess: function(data) {
            SmartAuth.showSuccess(smartAuth.strings.success);
            
            // Redirect if URL provided
            if (data.redirect_url) {
                setTimeout(function() {
                    window.location.href = data.redirect_url;
                }, 1500);
            } else {
                // Reload page to show logged in state
                setTimeout(function() {
                    window.location.reload();
                }, 1500);
            }
        },
        
        /**
         * Show OTP form
         */
        showOTPForm: function(phoneNumber, sessionInfo) {
            var $form = $('.smart-auth-form');
            var otpFormHtml = '<div class="smart-auth-otp-section">' +
                '<form class="smart-auth-otp-form">' +
                '<input type="hidden" class="smart-auth-phone-number" value="' + phoneNumber + '">' +
                '<input type="hidden" class="smart-auth-session-info" value="' + sessionInfo + '">' +
                '<label for="otp-code">' + smartAuth.strings.otpCode + '</label>' +
                '<input type="text" class="smart-auth-otp-code" id="otp-code" maxlength="6" required>' +
                '<button type="submit" class="smart-auth-verify-otp">' + smartAuth.strings.verify + '</button>' +
                '<button type="button" class="smart-auth-resend-otp">' + smartAuth.strings.resend + '</button>' +
                '</form>' +
                '</div>';
            
            $form.find('.smart-auth-providers').hide();
            $form.append(otpFormHtml);
        },
        
        /**
         * Show loading state
         */
        showLoading: function($button) {
            $button.prop('disabled', true).addClass('loading');
            $button.data('original-text', $button.text());
            $button.text(smartAuth.strings.loading);
        },
        
        /**
         * Hide loading state
         */
        hideLoading: function($button) {
            $button.prop('disabled', false).removeClass('loading');
            $button.text($button.data('original-text') || $button.text());
        },
        
        /**
         * Show error message
         */
        showError: function(message) {
            SmartAuth.showMessage(message, 'error');
        },
        
        /**
         * Show success message
         */
        showSuccess: function(message) {
            SmartAuth.showMessage(message, 'success');
        },
        
        /**
         * Show message
         */
        showMessage: function(message, type) {
            var $message = $('<div class="smart-auth-message smart-auth-' + type + '">' + message + '</div>');
            $('.smart-auth-form').prepend($message);
            
            setTimeout(function() {
                $message.fadeOut(function() {
                    $message.remove();
                });
            }, 5000);
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        SmartAuth.init();
    });
    
})(jQuery);
