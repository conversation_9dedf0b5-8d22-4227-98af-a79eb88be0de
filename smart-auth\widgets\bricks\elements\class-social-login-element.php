<?php
/**
 * Bricks Social Login Element
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Social Login Buttons Element for Bricks Builder
 */
class Smart_Auth_Bricks_Social_Login_Element extends \Bricks\Element {
    
    /**
     * Element category
     *
     * @var string
     */
    public $category = 'general';
    
    /**
     * Element name
     *
     * @var string
     */
    public $name = 'smart-auth-social-login';
    
    /**
     * Element icon
     *
     * @var string
     */
    public $icon = 'ti-share';
    
    /**
     * Element CSS selector
     *
     * @var string
     */
    public $css_selector = '.smart-auth-social-login-wrapper';
    
    /**
     * Element scripts
     *
     * @var array
     */
    public $scripts = array('smartAuthSocialLogin');
    
    /**
     * Element is nestable
     *
     * @var bool
     */
    public $nestable = false;
    
    /**
     * Get element label
     *
     * @return string Element label
     */
    public function get_label() {
        return __('Social Login Buttons', 'smart-auth');
    }
    
    /**
     * Get element keywords
     *
     * @return array Element keywords
     */
    public function get_keywords() {
        return array('social', 'login', 'google', 'facebook', 'apple', 'authentication');
    }
    
    /**
     * Set control groups
     */
    public function set_control_groups() {
        $this->control_groups['content'] = array(
            'title' => __('Content', 'smart-auth'),
            'tab' => 'content',
        );
        
        $this->control_groups['providers'] = array(
            'title' => __('Providers', 'smart-auth'),
            'tab' => 'content',
        );
        
        $this->control_groups['layout'] = array(
            'title' => __('Layout', 'smart-auth'),
            'tab' => 'content',
        );
        
        $this->control_groups['settings'] = array(
            'title' => __('Settings', 'smart-auth'),
            'tab' => 'content',
        );
    }
    
    /**
     * Set controls
     */
    public function set_controls() {
        // Content controls
        $this->controls['widget_title'] = array(
            'tab' => 'content',
            'group' => 'content',
            'label' => __('Widget Title', 'smart-auth'),
            'type' => 'text',
            'default' => __('Sign in with', 'smart-auth'),
        );
        
        $this->controls['show_title'] = array(
            'tab' => 'content',
            'group' => 'content',
            'label' => __('Show Title', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
        
        // Provider controls
        $this->controls['enable_google'] = array(
            'tab' => 'content',
            'group' => 'providers',
            'label' => __('Enable Google', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
        
        $this->controls['google_text'] = array(
            'tab' => 'content',
            'group' => 'providers',
            'label' => __('Google Button Text', 'smart-auth'),
            'type' => 'text',
            'default' => __('Continue with Google', 'smart-auth'),
            'required' => array('enable_google', '=', true),
        );
        
        $this->controls['enable_facebook'] = array(
            'tab' => 'content',
            'group' => 'providers',
            'label' => __('Enable Facebook', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
        
        $this->controls['facebook_text'] = array(
            'tab' => 'content',
            'group' => 'providers',
            'label' => __('Facebook Button Text', 'smart-auth'),
            'type' => 'text',
            'default' => __('Continue with Facebook', 'smart-auth'),
            'required' => array('enable_facebook', '=', true),
        );
        
        $this->controls['enable_apple'] = array(
            'tab' => 'content',
            'group' => 'providers',
            'label' => __('Enable Apple', 'smart-auth'),
            'type' => 'checkbox',
            'default' => false,
        );
        
        $this->controls['apple_text'] = array(
            'tab' => 'content',
            'group' => 'providers',
            'label' => __('Apple Button Text', 'smart-auth'),
            'type' => 'text',
            'default' => __('Continue with Apple', 'smart-auth'),
            'required' => array('enable_apple', '=', true),
        );
        
        // Layout controls
        $this->controls['button_layout'] = array(
            'tab' => 'content',
            'group' => 'layout',
            'label' => __('Button Layout', 'smart-auth'),
            'type' => 'select',
            'options' => array(
                'vertical' => __('Vertical', 'smart-auth'),
                'horizontal' => __('Horizontal', 'smart-auth'),
                'grid' => __('Grid', 'smart-auth'),
            ),
            'default' => 'vertical',
        );
        
        $this->controls['button_alignment'] = array(
            'tab' => 'content',
            'group' => 'layout',
            'label' => __('Button Alignment', 'smart-auth'),
            'type' => 'select',
            'options' => array(
                'left' => __('Left', 'smart-auth'),
                'center' => __('Center', 'smart-auth'),
                'right' => __('Right', 'smart-auth'),
            ),
            'default' => 'center',
        );
        
        $this->controls['show_icons'] = array(
            'tab' => 'content',
            'group' => 'layout',
            'label' => __('Show Icons', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
        
        $this->controls['button_width'] = array(
            'tab' => 'content',
            'group' => 'layout',
            'label' => __('Button Width', 'smart-auth'),
            'type' => 'select',
            'options' => array(
                'auto' => __('Auto', 'smart-auth'),
                'full' => __('Full Width', 'smart-auth'),
                'custom' => __('Custom', 'smart-auth'),
            ),
            'default' => 'full',
        );
        
        $this->controls['custom_button_width'] = array(
            'tab' => 'content',
            'group' => 'layout',
            'label' => __('Custom Width', 'smart-auth'),
            'type' => 'number',
            'units' => true,
            'default' => '300px',
            'required' => array('button_width', '=', 'custom'),
        );
        
        // Settings controls
        $this->controls['redirect_url'] = array(
            'tab' => 'content',
            'group' => 'settings',
            'label' => __('Redirect URL', 'smart-auth'),
            'type' => 'text',
            'default' => '',
            'placeholder' => 'https://your-domain.com',
        );
        
        $this->controls['enable_loading'] = array(
            'tab' => 'content',
            'group' => 'settings',
            'label' => __('Enable Loading State', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
        
        $this->controls['custom_css_class'] = array(
            'tab' => 'content',
            'group' => 'settings',
            'label' => __('Custom CSS Class', 'smart-auth'),
            'type' => 'text',
            'default' => '',
        );
    }
    
    /**
     * Enqueue element scripts
     */
    public function enqueue_scripts() {
        wp_enqueue_script(
            'smart-auth-bricks-social',
            SMART_AUTH_PLUGIN_URL . 'assets/js/bricks-social.js',
            array('jquery'),
            SMART_AUTH_VERSION,
            true
        );
    }
    
    /**
     * Render element
     */
    public function render() {
        $settings = $this->settings;
        
        // Get element settings
        $widget_title = isset($settings['widget_title']) ? $settings['widget_title'] : __('Sign in with', 'smart-auth');
        $show_title = isset($settings['show_title']) ? $settings['show_title'] : true;
        $button_layout = isset($settings['button_layout']) ? $settings['button_layout'] : 'vertical';
        $button_alignment = isset($settings['button_alignment']) ? $settings['button_alignment'] : 'center';
        $show_icons = isset($settings['show_icons']) ? $settings['show_icons'] : true;
        $button_width = isset($settings['button_width']) ? $settings['button_width'] : 'full';
        $custom_button_width = isset($settings['custom_button_width']) ? $settings['custom_button_width'] : '300px';
        $redirect_url = isset($settings['redirect_url']) ? $settings['redirect_url'] : '';
        $enable_loading = isset($settings['enable_loading']) ? $settings['enable_loading'] : true;
        $custom_css_class = isset($settings['custom_css_class']) ? $settings['custom_css_class'] : '';
        
        // Build enabled providers array
        $enabled_providers = array();
        if (isset($settings['enable_google']) && $settings['enable_google']) {
            $enabled_providers['google'] = isset($settings['google_text']) ? $settings['google_text'] : __('Continue with Google', 'smart-auth');
        }
        if (isset($settings['enable_facebook']) && $settings['enable_facebook']) {
            $enabled_providers['facebook'] = isset($settings['facebook_text']) ? $settings['facebook_text'] : __('Continue with Facebook', 'smart-auth');
        }
        if (isset($settings['enable_apple']) && $settings['enable_apple']) {
            $enabled_providers['apple'] = isset($settings['apple_text']) ? $settings['apple_text'] : __('Continue with Apple', 'smart-auth');
        }
        
        // Set wrapper attributes
        $wrapper_classes = array(
            'smart-auth-social-login-wrapper',
            'layout-' . $button_layout,
            'align-' . $button_alignment,
            'width-' . $button_width,
        );
        
        if (!empty($custom_css_class)) {
            $wrapper_classes[] = $custom_css_class;
        }
        
        if ($enable_loading) {
            $wrapper_classes[] = 'enable-loading';
        }
        
        $this->set_attribute('wrapper', 'class', implode(' ', $wrapper_classes));
        $this->set_attribute('wrapper', 'data-redirect-url', $redirect_url);
        
        if ($button_width === 'custom' && !empty($custom_button_width)) {
            $this->set_attribute('wrapper', 'style', '--custom-button-width: ' . $custom_button_width);
        }
        
        // Render element
        echo "<div {$this->render_attributes('wrapper')}>";
        
        if ($show_title && !empty($widget_title)) {
            echo '<h3 class="smart-auth-social-title">' . esc_html($widget_title) . '</h3>';
        }
        
        if (empty($enabled_providers)) {
            echo '<p class="smart-auth-no-providers">' . esc_html__('No social login providers enabled.', 'smart-auth') . '</p>';
        } else {
            echo '<div class="smart-auth-social-buttons">';
            
            foreach ($enabled_providers as $provider => $text) {
                echo '<button type="button" class="smart-auth-button smart-auth-' . esc_attr($provider) . '-button" data-provider="' . esc_attr($provider) . '">';
                
                if ($show_icons) {
                    echo '<span class="button-icon">' . $this->get_provider_icon($provider) . '</span>';
                }
                
                echo '<span class="button-text">' . esc_html($text) . '</span>';
                echo '</button>';
            }
            
            echo '</div>';
        }
        
        echo '</div>';
    }
    
    /**
     * Get provider icon
     *
     * @param string $provider Provider name
     * @return string Icon HTML
     */
    private function get_provider_icon($provider) {
        $icons = array(
            'google' => '<svg width="18" height="18" viewBox="0 0 18 18"><path fill="#4285F4" d="M16.51 8H8.98v3h4.3c-.18 1-.74 1.48-1.6 2.04v2.01h2.6a7.8 7.8 0 0 0 2.38-5.88c0-.57-.05-.66-.15-1.18z"/><path fill="#34A853" d="M8.98 17c2.16 0 3.97-.72 5.3-1.94l-2.6-2.04a4.8 4.8 0 0 1-7.18-2.53H1.83v2.07A8 8 0 0 0 8.98 17z"/><path fill="#FBBC05" d="M4.5 10.49a4.8 4.8 0 0 1 0-3.07V5.35H1.83a8 8 0 0 0 0 7.28l2.67-2.14z"/><path fill="#EA4335" d="M8.98 4.72c1.16 0 2.23.4 3.06 1.2l2.3-2.3A8 8 0 0 0 1.83 5.35L4.5 7.42a4.77 4.77 0 0 1 4.48-2.7z"/></svg>',
            'facebook' => '<svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>',
            'apple' => '<svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M12.017 0C8.396 0 8.025.044 8.025.044c0 .467.02 1.05.02 1.05C8.045 3.65 9.792 5.474 12.017 5.474s3.972-1.824 3.972-4.38c0 0-.03-.583-.03-1.05C15.959.044 15.588 0 12.017 0zm7.624 9.409c-.877-1.426-2.08-2.141-3.467-2.141-1.235 0-2.185.728-3.467.728-1.282 0-2.232-.728-3.467-.728-1.387 0-2.59.715-3.467 2.141C4.895 10.835 4.5 12.5 4.5 14.5c0 4.5 2.5 9.5 5.5 9.5 1.5 0 2.5-1 3.5-1s2 1 3.5 1c3 0 5.5-5 5.5-9.5 0-2-0.395-3.665-1.359-5.091z"/></svg>',
        );
        
        return isset($icons[$provider]) ? $icons[$provider] : '';
    }
}
