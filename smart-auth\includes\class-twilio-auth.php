<?php
/**
 * Twilio Authentication Handler
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

use libphonenumber\PhoneNumberUtil;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\NumberParseException;

/**
 * Twilio Authentication class
 */
class Smart_Auth_Twilio {

    /**
     * Twilio settings
     *
     * @var array
     */
    private $settings;

    /**
     * Twilio Verify API base URL
     *
     * @var string
     */
    private $api_base_url = 'https://verify.twilio.com/v2';

    /**
     * HTTP client
     *
     * @var GuzzleHttp\Client
     */
    private $http_client;

    /**
     * Phone number utility
     *
     * @var PhoneNumberUtil
     */
    private $phone_util;

    /**
     * Constructor
     */
    public function __construct() {
        $this->settings = get_option('smart_auth_twilio_settings', array());

        // Initialize phone number utility
        if (class_exists('libphonenumber\PhoneNumberUtil')) {
            $this->phone_util = PhoneNumberUtil::getInstance();
        }

        // Initialize HTTP client if Guzzle is available
        if (class_exists('GuzzleHttp\Client') && !empty($this->settings['account_sid']) && !empty($this->settings['auth_token'])) {
            $this->http_client = new \GuzzleHttp\Client([
                'base_uri' => $this->api_base_url,
                'timeout' => 30,
                'auth' => [$this->settings['account_sid'], $this->settings['auth_token']],
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                    'User-Agent' => 'Smart-Auth-WordPress-Plugin/1.0.0',
                ],
            ]);
        }
    }

    /**
     * Send OTP via Twilio Verify
     *
     * @param string $phone_number Phone number in E.164 format
     * @return array|WP_Error Response or error
     */
    public function send_otp($phone_number) {
        if (!$this->is_configured()) {
            return new WP_Error('twilio_not_configured', __('Twilio is not configured.', 'smart-auth'));
        }

        if (!$this->http_client) {
            return new WP_Error('http_client_missing', __('HTTP client not available.', 'smart-auth'));
        }

        // Validate and format phone number
        $formatted_phone = $this->format_phone_number($phone_number);
        if (is_wp_error($formatted_phone)) {
            return $formatted_phone;
        }

        // Check rate limiting
        $rate_limit_check = $this->check_rate_limit($formatted_phone);
        if (is_wp_error($rate_limit_check)) {
            return $rate_limit_check;
        }

        try {
            $response = $this->http_client->post('/Services/' . $this->settings['verify_service_sid'] . '/Verifications', [
                'form_params' => [
                    'To' => $formatted_phone,
                    'Channel' => 'sms',
                ],
            ]);

            $body = json_decode($response->getBody()->getContents(), true);

            if ($response->getStatusCode() !== 201) {
                $error_message = isset($body['message']) ? $body['message'] : __('Failed to send OTP.', 'smart-auth');
                return new WP_Error('twilio_send_error', $error_message);
            }

            // Update rate limiting
            $this->update_rate_limit($formatted_phone);

            return array(
                'sid' => $body['sid'],
                'phone_number' => $formatted_phone,
                'status' => $body['status'],
                'channel' => $body['channel'],
            );

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            $error_message = $this->extract_twilio_error($e);
            error_log('Smart Auth Twilio Send Error: ' . $error_message);
            return new WP_Error('twilio_request_error', $error_message);
        }
    }

    /**
     * Verify OTP code
     *
     * @param string $phone_number Phone number in E.164 format
     * @param string $otp_code OTP code
     * @return array|WP_Error Response or error
     */
    public function verify_otp($phone_number, $otp_code) {
        if (!$this->is_configured()) {
            return new WP_Error('twilio_not_configured', __('Twilio is not configured.', 'smart-auth'));
        }

        if (!$this->http_client) {
            return new WP_Error('http_client_missing', __('HTTP client not available.', 'smart-auth'));
        }

        // Validate and format phone number
        $formatted_phone = $this->format_phone_number($phone_number);
        if (is_wp_error($formatted_phone)) {
            return $formatted_phone;
        }

        if (empty($otp_code)) {
            return new WP_Error('missing_otp', __('OTP code is required.', 'smart-auth'));
        }

        try {
            $response = $this->http_client->post('/Services/' . $this->settings['verify_service_sid'] . '/VerificationCheck', [
                'form_params' => [
                    'To' => $formatted_phone,
                    'Code' => $otp_code,
                ],
            ]);

            $body = json_decode($response->getBody()->getContents(), true);

            if ($response->getStatusCode() !== 200) {
                $error_message = isset($body['message']) ? $body['message'] : __('Failed to verify OTP.', 'smart-auth');
                return new WP_Error('twilio_verify_error', $error_message);
            }

            if ($body['status'] !== 'approved') {
                return new WP_Error('otp_invalid', __('Invalid OTP code.', 'smart-auth'));
            }

            return array(
                'sid' => $body['sid'],
                'phone_number' => $formatted_phone,
                'status' => $body['status'],
                'valid' => $body['valid'],
            );

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            $error_message = $this->extract_twilio_error($e);
            error_log('Smart Auth Twilio Verify Error: ' . $error_message);
            return new WP_Error('twilio_request_error', $error_message);
        }
    }

    /**
     * Test Twilio connection
     *
     * @return bool|WP_Error True if connection successful, error otherwise
     */
    public function test_connection() {
        if (!$this->is_configured()) {
            return new WP_Error('twilio_not_configured', __('Twilio configuration is incomplete.', 'smart-auth'));
        }

        if (!$this->http_client) {
            return new WP_Error('http_client_missing', __('HTTP client not available.', 'smart-auth'));
        }

        try {
            // Test by getting the Verify service details
            $response = $this->http_client->get('/Services/' . $this->settings['verify_service_sid']);

            $body = json_decode($response->getBody()->getContents(), true);

            if ($response->getStatusCode() !== 200) {
                $error_message = isset($body['message']) ? $body['message'] : __('Failed to connect to Twilio.', 'smart-auth');
                return new WP_Error('twilio_connection_error', $error_message);
            }

            // Check if the service is active
            if (isset($body['status']) && $body['status'] !== 'active') {
                return new WP_Error('service_inactive', __('Twilio Verify service is not active.', 'smart-auth'));
            }

            return true;

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            $error_message = $this->extract_twilio_error($e);
            error_log('Smart Auth Twilio Test Error: ' . $error_message);
            return new WP_Error('twilio_connection_error', $error_message);
        }
    }

    /**
     * Format phone number to E.164 format
     *
     * @param string $phone_number Phone number to format
     * @param string $region Default region code (ISO 3166-1 alpha-2)
     * @return string|WP_Error Formatted phone number or error
     */
    public function format_phone_number($phone_number, $region = 'US') {
        if (!$this->phone_util) {
            // Fallback to basic validation if libphonenumber is not available
            if (preg_match('/^\+[1-9]\d{1,14}$/', $phone_number)) {
                return $phone_number;
            } else {
                return new WP_Error('invalid_phone', __('Invalid phone number format.', 'smart-auth'));
            }
        }

        try {
            $parsed_number = $this->phone_util->parse($phone_number, $region);

            if (!$this->phone_util->isValidNumber($parsed_number)) {
                return new WP_Error('invalid_phone', __('Invalid phone number.', 'smart-auth'));
            }

            return $this->phone_util->format($parsed_number, PhoneNumberFormat::E164);

        } catch (NumberParseException $e) {
            return new WP_Error('phone_parse_error', __('Failed to parse phone number.', 'smart-auth'));
        }
    }

    /**
     * Check if Twilio is properly configured
     *
     * @return bool True if configured, false otherwise
     */
    private function is_configured() {
        return !empty($this->settings['account_sid']) &&
               !empty($this->settings['auth_token']) &&
               !empty($this->settings['verify_service_sid']);
    }

    /**
     * Check rate limiting for phone number
     *
     * @param string $phone_number Phone number to check
     * @return bool|WP_Error True if allowed, error if rate limited
     */
    private function check_rate_limit($phone_number) {
        $security_settings = get_option('smart_auth_security_settings', array());
        $max_attempts = isset($security_settings['rate_limit_attempts']) ? (int) $security_settings['rate_limit_attempts'] : 3;
        $time_window = isset($security_settings['rate_limit_window']) ? (int) $security_settings['rate_limit_window'] : 15 * MINUTE_IN_SECONDS;

        $cache_key = 'smart_auth_rate_limit_' . md5($phone_number);
        $attempts = get_transient($cache_key);

        if ($attempts === false) {
            $attempts = 0;
        }

        if ($attempts >= $max_attempts) {
            return new WP_Error('rate_limited', sprintf(
                __('Too many OTP requests. Please wait %d minutes before trying again.', 'smart-auth'),
                ceil($time_window / 60)
            ));
        }

        return true;
    }

    /**
     * Update rate limiting for phone number
     *
     * @param string $phone_number Phone number to update
     */
    private function update_rate_limit($phone_number) {
        $security_settings = get_option('smart_auth_security_settings', array());
        $time_window = isset($security_settings['rate_limit_window']) ? (int) $security_settings['rate_limit_window'] : 15 * MINUTE_IN_SECONDS;

        $cache_key = 'smart_auth_rate_limit_' . md5($phone_number);
        $attempts = get_transient($cache_key);

        if ($attempts === false) {
            $attempts = 0;
        }

        set_transient($cache_key, $attempts + 1, $time_window);
    }

    /**
     * Extract error message from Twilio exception
     *
     * @param \GuzzleHttp\Exception\RequestException $e Exception
     * @return string Error message
     */
    private function extract_twilio_error($e) {
        if ($e->hasResponse()) {
            $response_body = $e->getResponse()->getBody()->getContents();
            $error_data = json_decode($response_body, true);

            if (isset($error_data['message'])) {
                return $error_data['message'];
            }
        }

        return $e->getMessage();
    }

    /**
     * Get Twilio settings
     *
     * @return array Twilio settings
     */
    public function get_settings() {
        return $this->settings;
    }

    /**
     * Update Twilio settings
     *
     * @param array $settings New settings
     * @return bool True on success, false on failure
     */
    public function update_settings($settings) {
        $this->settings = $settings;
        return update_option('smart_auth_twilio_settings', $settings);
    }

    /**
     * Get supported phone number regions
     *
     * @return array Array of region codes and names
     */
    public function get_supported_regions() {
        return array(
            'US' => __('United States', 'smart-auth'),
            'CA' => __('Canada', 'smart-auth'),
            'GB' => __('United Kingdom', 'smart-auth'),
            'AU' => __('Australia', 'smart-auth'),
            'DE' => __('Germany', 'smart-auth'),
            'FR' => __('France', 'smart-auth'),
            'IT' => __('Italy', 'smart-auth'),
            'ES' => __('Spain', 'smart-auth'),
            'BR' => __('Brazil', 'smart-auth'),
            'IN' => __('India', 'smart-auth'),
            'JP' => __('Japan', 'smart-auth'),
            'KR' => __('South Korea', 'smart-auth'),
            'CN' => __('China', 'smart-auth'),
        );
    }
}
