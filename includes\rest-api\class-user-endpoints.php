<?php
/**
 * User REST API Endpoints
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * User REST API endpoints class
 */
class Smart_Auth_User_Endpoints {
    
    /**
     * API namespace
     *
     * @var string
     */
    private $namespace = 'smart-auth/v1';
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('rest_api_init', array($this, 'register_routes'));
    }
    
    /**
     * Register REST API routes
     */
    public function register_routes() {
        // Get current user profile
        register_rest_route($this->namespace, '/user-profile', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_user_profile'),
            'permission_callback' => array($this, 'check_jwt_auth'),
        ));
        
        // Update user profile
        register_rest_route($this->namespace, '/user-profile', array(
            'methods' => 'POST',
            'callback' => array($this, 'update_user_profile'),
            'permission_callback' => array($this, 'check_jwt_auth'),
            'args' => array(
                'display_name' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'email' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_email',
                ),
                'phone_number' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
            ),
        ));
        
        // Sync user data from Firebase
        register_rest_route($this->namespace, '/sync-firebase', array(
            'methods' => 'POST',
            'callback' => array($this, 'sync_firebase_data'),
            'permission_callback' => array($this, 'check_jwt_auth'),
            'args' => array(
                'firebase_user' => array(
                    'required' => true,
                    'type' => 'object',
                ),
            ),
        ));
    }
    
    /**
     * Get current user profile
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function get_user_profile($request) {
        // Implementation will be added in the next task
        return new WP_Error('not_implemented', 'Get user profile endpoint not implemented yet', array('status' => 501));
    }
    
    /**
     * Update user profile
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function update_user_profile($request) {
        // Implementation will be added in the next task
        return new WP_Error('not_implemented', 'Update user profile endpoint not implemented yet', array('status' => 501));
    }
    
    /**
     * Sync Firebase data
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function sync_firebase_data($request) {
        // Implementation will be added in the next task
        return new WP_Error('not_implemented', 'Sync Firebase data endpoint not implemented yet', array('status' => 501));
    }
    
    /**
     * Check JWT authentication
     *
     * @param WP_REST_Request $request Request object
     * @return bool|WP_Error True if authenticated, error otherwise
     */
    public function check_jwt_auth($request) {
        // Implementation will be added in the next task
        return new WP_Error('not_implemented', 'JWT auth check not implemented yet', array('status' => 501));
    }
}
