<?php
/**
 * User REST API Endpoints
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * User REST API endpoints class
 */
class Smart_Auth_User_Endpoints {
    
    /**
     * API namespace
     *
     * @var string
     */
    private $namespace = 'smart-auth/v1';
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('rest_api_init', array($this, 'register_routes'));
    }
    
    /**
     * Register REST API routes
     */
    public function register_routes() {
        // Get current user profile
        register_rest_route($this->namespace, '/user-profile', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_user_profile'),
            'permission_callback' => array($this, 'check_jwt_auth'),
        ));
        
        // Update user profile
        register_rest_route($this->namespace, '/user-profile', array(
            'methods' => 'POST',
            'callback' => array($this, 'update_user_profile'),
            'permission_callback' => array($this, 'check_jwt_auth'),
            'args' => array(
                'display_name' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'email' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_email',
                ),
                'phone_number' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
            ),
        ));
        
        // Sync user data from Firebase
        register_rest_route($this->namespace, '/sync-firebase', array(
            'methods' => 'POST',
            'callback' => array($this, 'sync_firebase_data'),
            'permission_callback' => array($this, 'check_jwt_auth'),
            'args' => array(
                'firebase_user' => array(
                    'required' => true,
                    'type' => 'object',
                ),
            ),
        ));
    }
    
    /**
     * Get current user profile
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function get_user_profile($request) {
        $current_user = wp_get_current_user();

        if (!$current_user || $current_user->ID === 0) {
            return new WP_Error('not_authenticated', __('User not authenticated.', 'smart-auth'), array('status' => 401));
        }

        // Get user meta
        $firebase_uid = get_user_meta($current_user->ID, 'firebase_uid', true);
        $auth_provider = get_user_meta($current_user->ID, 'auth_provider', true);
        $phone_number = get_user_meta($current_user->ID, 'phone_number', true);
        $profile_picture_url = get_user_meta($current_user->ID, 'profile_picture_url', true);
        $email_verified = get_user_meta($current_user->ID, 'email_verified', true);
        $last_firebase_sync = get_user_meta($current_user->ID, 'last_firebase_sync', true);
        $firebase_custom_claims = get_user_meta($current_user->ID, 'firebase_custom_claims', true);

        // Get avatar URL
        $avatar_url = get_avatar_url($current_user->ID, array('size' => 96));

        // Build profile data
        $profile_data = array(
            'user_id' => $current_user->ID,
            'user_login' => $current_user->user_login,
            'user_email' => $current_user->user_email,
            'user_nicename' => $current_user->user_nicename,
            'display_name' => $current_user->display_name,
            'first_name' => $current_user->first_name,
            'last_name' => $current_user->last_name,
            'user_registered' => $current_user->user_registered,
            'roles' => $current_user->roles,
            'capabilities' => array_keys($current_user->allcaps),
            'avatar_url' => $avatar_url,
            'firebase_uid' => $firebase_uid,
            'auth_provider' => $auth_provider,
            'phone_number' => $phone_number,
            'profile_picture_url' => $profile_picture_url,
            'email_verified' => (bool) $email_verified,
            'last_firebase_sync' => $last_firebase_sync,
            'firebase_custom_claims' => $firebase_custom_claims ?: array(),
        );

        // Apply filter to allow customization
        $profile_data = apply_filters('smart_auth_user_profile_data', $profile_data, $current_user);

        return new WP_REST_Response(array(
            'success' => true,
            'data' => $profile_data,
        ), 200);
    }
    
    /**
     * Update user profile
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function update_user_profile($request) {
        $current_user = wp_get_current_user();

        if (!$current_user || $current_user->ID === 0) {
            return new WP_Error('not_authenticated', __('User not authenticated.', 'smart-auth'), array('status' => 401));
        }

        // Get parameters
        $display_name = $request->get_param('display_name');
        $email = $request->get_param('email');
        $phone_number = $request->get_param('phone_number');

        $user_data = array('ID' => $current_user->ID);
        $updated_fields = array();

        // Update display name
        if (!empty($display_name) && $display_name !== $current_user->display_name) {
            $user_data['display_name'] = sanitize_text_field($display_name);
            $updated_fields[] = 'display_name';
        }

        // Update email (with validation)
        if (!empty($email) && $email !== $current_user->user_email) {
            if (!is_email($email)) {
                return new WP_Error('invalid_email', __('Invalid email address.', 'smart-auth'), array('status' => 400));
            }

            // Check if email is already in use
            if (email_exists($email)) {
                return new WP_Error('email_exists', __('Email address is already in use.', 'smart-auth'), array('status' => 400));
            }

            $user_data['user_email'] = sanitize_email($email);
            $updated_fields[] = 'email';
        }

        // Update WordPress user data if there are changes
        if (count($user_data) > 1) {
            $result = wp_update_user($user_data);
            if (is_wp_error($result)) {
                return $result;
            }
        }

        // Update phone number in user meta
        if (!empty($phone_number)) {
            $current_phone = get_user_meta($current_user->ID, 'phone_number', true);
            if ($phone_number !== $current_phone) {
                // Validate phone number format
                $twilio_auth = new Smart_Auth_Twilio();
                $formatted_phone = $twilio_auth->format_phone_number($phone_number);

                if (is_wp_error($formatted_phone)) {
                    return $formatted_phone;
                }

                update_user_meta($current_user->ID, 'phone_number', $formatted_phone);
                $updated_fields[] = 'phone_number';
            }
        }

        // Fire action for profile update
        if (!empty($updated_fields)) {
            do_action('smart_auth_user_profile_updated', $current_user->ID, $updated_fields, $request);
        }

        return new WP_REST_Response(array(
            'success' => true,
            'message' => __('Profile updated successfully.', 'smart-auth'),
            'data' => array(
                'updated_fields' => $updated_fields,
            ),
        ), 200);
    }
    
    /**
     * Sync Firebase data
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function sync_firebase_data($request) {
        $current_user = wp_get_current_user();

        if (!$current_user || $current_user->ID === 0) {
            return new WP_Error('not_authenticated', __('User not authenticated.', 'smart-auth'), array('status' => 401));
        }

        // Get parameters
        $firebase_user = $request->get_param('firebase_user');

        if (empty($firebase_user) || !is_array($firebase_user)) {
            return new WP_Error('missing_firebase_data', __('Firebase user data is required.', 'smart-auth'), array('status' => 400));
        }

        // Validate Firebase UID matches current user
        $current_firebase_uid = get_user_meta($current_user->ID, 'firebase_uid', true);
        if (empty($current_firebase_uid) || $current_firebase_uid !== $firebase_user['firebase_uid']) {
            return new WP_Error('firebase_uid_mismatch', __('Firebase UID does not match current user.', 'smart-auth'), array('status' => 403));
        }

        // Get user sync handler
        $user_sync = new Smart_Auth_User_Sync();
        $auth_provider = get_user_meta($current_user->ID, 'auth_provider', true) ?: 'firebase';

        // Update user data
        $updated_user = $user_sync->sync_user($firebase_user, $auth_provider);
        if (is_wp_error($updated_user)) {
            return $updated_user;
        }

        return new WP_REST_Response(array(
            'success' => true,
            'message' => __('Firebase data synchronized successfully.', 'smart-auth'),
            'data' => array(
                'user_id' => $updated_user->ID,
                'last_sync' => current_time('mysql'),
            ),
        ), 200);
    }

    /**
     * Check JWT authentication
     *
     * @param WP_REST_Request $request Request object
     * @return bool|WP_Error True if authenticated, error otherwise
     */
    public function check_jwt_auth($request) {
        // Get JWT handler
        $jwt_handler = new Smart_Auth_JWT_Handler();

        // Get token from header
        $token = $jwt_handler->get_token_from_header();

        if (empty($token)) {
            return new WP_Error('missing_token', __('JWT token is required.', 'smart-auth'), array('status' => 401));
        }

        // Validate token
        $payload = $jwt_handler->validate_token($token);
        if (is_wp_error($payload)) {
            return $payload;
        }

        // Set current user
        if (isset($payload['data']['user']['id'])) {
            wp_set_current_user($payload['data']['user']['id']);
        }

        return true;
    }
}
