/**
 * Smart Auth Firebase Configuration
 *
 * @package SmartAuth
 * @since 1.0.0
 */

(function(window) {
    'use strict';
    
    /**
     * Firebase Configuration Manager
     */
    window.SmartAuthFirebase = {
        
        /**
         * Firebase app instance
         */
        app: null,
        
        /**
         * Firebase auth instance
         */
        auth: null,
        
        /**
         * Firebase configuration
         */
        config: null,
        
        /**
         * Initialize Firebase
         */
        init: function(config) {
            if (!config || !config.apiKey || !config.projectId) {
                console.error('Smart Auth: Firebase configuration is incomplete');
                return false;
            }
            
            this.config = config;
            
            try {
                // Initialize Firebase app
                this.app = firebase.initializeApp(config);
                this.auth = firebase.auth();
                
                // Configure auth settings
                this.auth.useDeviceLanguage();
                
                // Set up auth state listener
                this.setupAuthStateListener();
                
                console.log('Smart Auth: Firebase initialized successfully');
                return true;
                
            } catch (error) {
                console.error('Smart Auth: Firebase initialization failed', error);
                return false;
            }
        },
        
        /**
         * Set up auth state listener
         */
        setupAuthStateListener: function() {
            var self = this;
            
            this.auth.onAuthStateChanged(function(user) {
                if (user) {
                    // User is signed in
                    self.handleAuthSuccess(user);
                } else {
                    // User is signed out
                    self.handleAuthSignOut();
                }
            });
        },
        
        /**
         * Sign in with Google
         */
        signInWithGoogle: function() {
            var provider = new firebase.auth.GoogleAuthProvider();
            provider.addScope('profile');
            provider.addScope('email');
            
            return this.signInWithPopup(provider);
        },
        
        /**
         * Sign in with Facebook
         */
        signInWithFacebook: function() {
            var provider = new firebase.auth.FacebookAuthProvider();
            provider.addScope('email');
            
            return this.signInWithPopup(provider);
        },
        
        /**
         * Sign in with Apple
         */
        signInWithApple: function() {
            var provider = new firebase.auth.OAuthProvider('apple.com');
            provider.addScope('email');
            provider.addScope('name');
            
            return this.signInWithPopup(provider);
        },
        
        /**
         * Sign in with popup
         */
        signInWithPopup: function(provider) {
            var self = this;
            
            return this.auth.signInWithPopup(provider)
                .then(function(result) {
                    return self.handleAuthResult(result);
                })
                .catch(function(error) {
                    return self.handleAuthError(error);
                });
        },
        
        /**
         * Send phone verification code
         */
        sendPhoneVerification: function(phoneNumber, recaptchaVerifier) {
            var self = this;
            
            return this.auth.signInWithPhoneNumber(phoneNumber, recaptchaVerifier)
                .then(function(confirmationResult) {
                    return {
                        success: true,
                        confirmationResult: confirmationResult,
                        sessionInfo: confirmationResult.verificationId
                    };
                })
                .catch(function(error) {
                    return self.handleAuthError(error);
                });
        },
        
        /**
         * Verify phone OTP
         */
        verifyPhoneOTP: function(confirmationResult, otpCode) {
            var self = this;
            
            return confirmationResult.confirm(otpCode)
                .then(function(result) {
                    return self.handleAuthResult(result);
                })
                .catch(function(error) {
                    return self.handleAuthError(error);
                });
        },
        
        /**
         * Handle successful authentication
         */
        handleAuthResult: function(result) {
            var self = this;
            var user = result.user;
            
            return user.getIdToken()
                .then(function(idToken) {
                    return {
                        success: true,
                        user: user,
                        idToken: idToken,
                        providerId: self.getProviderId(result),
                        userData: self.extractUserData(user, result)
                    };
                });
        },
        
        /**
         * Handle authentication success
         */
        handleAuthSuccess: function(user) {
            // Fire custom event
            var event = new CustomEvent('smartAuthSuccess', {
                detail: { user: user }
            });
            document.dispatchEvent(event);
        },
        
        /**
         * Handle authentication sign out
         */
        handleAuthSignOut: function() {
            // Fire custom event
            var event = new CustomEvent('smartAuthSignOut');
            document.dispatchEvent(event);
        },
        
        /**
         * Handle authentication error
         */
        handleAuthError: function(error) {
            console.error('Smart Auth Firebase Error:', error);
            
            var errorMessage = this.getErrorMessage(error);
            
            // Fire custom event
            var event = new CustomEvent('smartAuthError', {
                detail: { 
                    error: error,
                    message: errorMessage
                }
            });
            document.dispatchEvent(event);
            
            return {
                success: false,
                error: error,
                message: errorMessage
            };
        },
        
        /**
         * Get provider ID from auth result
         */
        getProviderId: function(result) {
            if (result.additionalUserInfo && result.additionalUserInfo.providerId) {
                var providerId = result.additionalUserInfo.providerId;
                
                switch (providerId) {
                    case 'google.com':
                        return 'google';
                    case 'facebook.com':
                        return 'facebook';
                    case 'apple.com':
                        return 'apple';
                    case 'phone':
                        return 'phone';
                    default:
                        return 'firebase';
                }
            }
            
            return 'firebase';
        },
        
        /**
         * Extract user data from Firebase user
         */
        extractUserData: function(user, result) {
            var userData = {
                uid: user.uid,
                email: user.email || '',
                emailVerified: user.emailVerified || false,
                displayName: user.displayName || '',
                photoURL: user.photoURL || '',
                phoneNumber: user.phoneNumber || ''
            };
            
            // Add additional user info if available
            if (result && result.additionalUserInfo) {
                var profile = result.additionalUserInfo.profile || {};
                
                // Google specific data
                if (result.additionalUserInfo.providerId === 'google.com') {
                    userData.firstName = profile.given_name || '';
                    userData.lastName = profile.family_name || '';
                }
                
                // Facebook specific data
                if (result.additionalUserInfo.providerId === 'facebook.com') {
                    userData.firstName = profile.first_name || '';
                    userData.lastName = profile.last_name || '';
                }
            }
            
            return userData;
        },
        
        /**
         * Get user-friendly error message
         */
        getErrorMessage: function(error) {
            switch (error.code) {
                case 'auth/cancelled-popup-request':
                case 'auth/popup-closed-by-user':
                    return 'Authentication was cancelled.';
                    
                case 'auth/popup-blocked':
                    return 'Popup was blocked by the browser. Please allow popups and try again.';
                    
                case 'auth/account-exists-with-different-credential':
                    return 'An account already exists with the same email address but different sign-in credentials.';
                    
                case 'auth/invalid-phone-number':
                    return 'Invalid phone number format.';
                    
                case 'auth/too-many-requests':
                    return 'Too many requests. Please try again later.';
                    
                case 'auth/invalid-verification-code':
                    return 'Invalid verification code.';
                    
                case 'auth/code-expired':
                    return 'Verification code has expired.';
                    
                default:
                    return error.message || 'An error occurred during authentication.';
            }
        },
        
        /**
         * Sign out current user
         */
        signOut: function() {
            return this.auth.signOut();
        },
        
        /**
         * Get current user
         */
        getCurrentUser: function() {
            return this.auth.currentUser;
        },
        
        /**
         * Check if user is signed in
         */
        isSignedIn: function() {
            return !!this.auth.currentUser;
        }
    };
    
})(window);
