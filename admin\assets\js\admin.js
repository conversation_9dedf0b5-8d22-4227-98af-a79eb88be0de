/**
 * Smart Auth Admin JavaScript
 *
 * @package SmartAuth
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    /**
     * Smart Auth Admin object
     */
    window.SmartAuthAdmin = {
        
        /**
         * Initialize admin functionality
         */
        init: function() {
            this.bindEvents();
            this.initTabs();
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            // Test connection buttons
            $(document).on('click', '#test-firebase-connection', this.testFirebaseConnection);
            $(document).on('click', '#test-twilio-connection', this.testTwilioConnection);
            $(document).on('click', '#generate-jwt-secret', this.generateJWTSecret);
            
            // Form validation
            $(document).on('submit', 'form[action="options.php"]', this.validateForm);
            
            // Dynamic field visibility
            $(document).on('change', 'input[name="smart_auth_security_settings[enable_captcha]"]', this.toggleCaptchaFields);
            $(document).on('change', 'input[name="smart_auth_security_settings[enable_ip_blocking]"]', this.toggleIPBlockingFields);
            $(document).on('change', 'input[name="smart_auth_user_settings[sync_profile_picture]"]', this.toggleProfilePictureFields);
            
            // Copy to clipboard functionality
            $(document).on('click', '.copy-to-clipboard', this.copyToClipboard);
        },
        
        /**
         * Initialize tab functionality
         */
        initTabs: function() {
            // Handle tab switching
            $('.nav-tab').on('click', function(e) {
                e.preventDefault();
                
                var $this = $(this);
                var tab = $this.attr('href').split('tab=')[1];
                
                // Update URL without page reload
                if (history.pushState) {
                    var newUrl = window.location.protocol + "//" + window.location.host + 
                                window.location.pathname + '?page=smart-auth-settings&tab=' + tab;
                    window.history.pushState({path: newUrl}, '', newUrl);
                }
                
                // Update active tab
                $('.nav-tab').removeClass('nav-tab-active');
                $this.addClass('nav-tab-active');
                
                // Show/hide tab content
                $('.tab-content > form').hide();
                $('.tab-content-' + tab).show();
            });
        },
        
        /**
         * Test Firebase connection
         */
        testFirebaseConnection: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $result = $('#firebase-test-result');
            
            // Show loading state
            $button.prop('disabled', true).text('Testing...');
            $result.removeClass('success error').text('');
            
            // Get form data
            var formData = {
                action: 'smart_auth_test_firebase',
                nonce: SmartAuthAdmin.getNonce(),
                project_id: $('input[name="smart_auth_firebase_settings[project_id]"]').val(),
                api_key: $('input[name="smart_auth_firebase_settings[api_key]"]').val(),
                auth_domain: $('input[name="smart_auth_firebase_settings[auth_domain]"]').val(),
                storage_bucket: $('input[name="smart_auth_firebase_settings[storage_bucket]"]').val()
            };
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        $result.addClass('success').text(response.data.message);
                    } else {
                        $result.addClass('error').text(response.data.message);
                    }
                },
                error: function() {
                    $result.addClass('error').text('Connection test failed. Please try again.');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Test Connection');
                }
            });
        },
        
        /**
         * Test Twilio connection
         */
        testTwilioConnection: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $result = $('#twilio-test-result');
            
            // Show loading state
            $button.prop('disabled', true).text('Testing...');
            $result.removeClass('success error').text('');
            
            // Get form data
            var formData = {
                action: 'smart_auth_test_twilio',
                nonce: SmartAuthAdmin.getNonce(),
                account_sid: $('input[name="smart_auth_twilio_settings[account_sid]"]').val(),
                auth_token: $('input[name="smart_auth_twilio_settings[auth_token]"]').val(),
                verify_service_sid: $('input[name="smart_auth_twilio_settings[verify_service_sid]"]').val()
            };
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        $result.addClass('success').text(response.data.message);
                    } else {
                        $result.addClass('error').text(response.data.message);
                    }
                },
                error: function() {
                    $result.addClass('error').text('Connection test failed. Please try again.');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Test Connection');
                }
            });
        },
        
        /**
         * Generate JWT secret
         */
        generateJWTSecret: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $input = $('input[name="smart_auth_jwt_settings[secret_key]"]');
            
            if (!confirm('Are you sure you want to generate a new JWT secret? This will invalidate all existing tokens.')) {
                return;
            }
            
            // Show loading state
            $button.prop('disabled', true).text('Generating...');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'smart_auth_generate_jwt_secret',
                    nonce: SmartAuthAdmin.getNonce()
                },
                success: function(response) {
                    if (response.success) {
                        $input.val(response.data.secret);
                        SmartAuthAdmin.showNotice(response.data.message, 'success');
                    } else {
                        SmartAuthAdmin.showNotice('Failed to generate secret.', 'error');
                    }
                },
                error: function() {
                    SmartAuthAdmin.showNotice('Failed to generate secret.', 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Generate New Secret');
                }
            });
        },
        
        /**
         * Validate form before submission
         */
        validateForm: function(e) {
            var $form = $(this);
            var isValid = true;
            var errors = [];
            
            // Firebase validation
            if ($form.find('input[name*="firebase"]').length > 0) {
                var projectId = $form.find('input[name="smart_auth_firebase_settings[project_id]"]').val();
                var apiKey = $form.find('input[name="smart_auth_firebase_settings[api_key]"]').val();
                
                if (projectId && !apiKey) {
                    errors.push('Firebase API Key is required when Project ID is provided.');
                    isValid = false;
                }
                
                if (apiKey && !projectId) {
                    errors.push('Firebase Project ID is required when API Key is provided.');
                    isValid = false;
                }
            }
            
            // Twilio validation
            if ($form.find('input[name*="twilio"]').length > 0) {
                var accountSid = $form.find('input[name="smart_auth_twilio_settings[account_sid]"]').val();
                var authToken = $form.find('input[name="smart_auth_twilio_settings[auth_token]"]').val();
                var serviceSid = $form.find('input[name="smart_auth_twilio_settings[verify_service_sid]"]').val();
                
                if ((accountSid || authToken || serviceSid) && (!accountSid || !authToken || !serviceSid)) {
                    errors.push('All Twilio fields are required when configuring Twilio.');
                    isValid = false;
                }
            }
            
            // Show errors if any
            if (!isValid) {
                e.preventDefault();
                SmartAuthAdmin.showNotice(errors.join('<br>'), 'error');
            }
            
            return isValid;
        },
        
        /**
         * Toggle CAPTCHA fields visibility
         */
        toggleCaptchaFields: function() {
            var $checkbox = $(this);
            var $fields = $checkbox.closest('fieldset').find('label').not(':first');
            
            if ($checkbox.is(':checked')) {
                $fields.show();
            } else {
                $fields.hide();
            }
        },
        
        /**
         * Toggle IP blocking fields visibility
         */
        toggleIPBlockingFields: function() {
            var $checkbox = $(this);
            var $textarea = $checkbox.closest('fieldset').find('textarea').closest('label');
            
            if ($checkbox.is(':checked')) {
                $textarea.show();
            } else {
                $textarea.hide();
            }
        },
        
        /**
         * Toggle profile picture fields visibility
         */
        toggleProfilePictureFields: function() {
            var $checkbox = $(this);
            var $downloadCheckbox = $('input[name="smart_auth_user_settings[download_profile_picture]"]').closest('label');
            
            if ($checkbox.is(':checked')) {
                $downloadCheckbox.show();
            } else {
                $downloadCheckbox.hide();
            }
        },
        
        /**
         * Copy text to clipboard
         */
        copyToClipboard: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var text = $button.data('text') || $button.prev('input').val();
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(function() {
                    SmartAuthAdmin.showNotice('Copied to clipboard!', 'success');
                });
            } else {
                // Fallback for older browsers
                var $temp = $('<textarea>');
                $('body').append($temp);
                $temp.val(text).select();
                document.execCommand('copy');
                $temp.remove();
                SmartAuthAdmin.showNotice('Copied to clipboard!', 'success');
            }
        },
        
        /**
         * Show admin notice
         */
        showNotice: function(message, type) {
            var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            $('.wrap h1').after($notice);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $notice.remove();
                });
            }, 5000);
        },
        
        /**
         * Get admin nonce
         */
        getNonce: function() {
            return $('#smart_auth_admin_nonce').val() || '';
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        SmartAuthAdmin.init();
        
        // Initialize field visibility on page load
        $('input[name="smart_auth_security_settings[enable_captcha]"]').trigger('change');
        $('input[name="smart_auth_security_settings[enable_ip_blocking]"]').trigger('change');
        $('input[name="smart_auth_user_settings[sync_profile_picture]"]').trigger('change');
    });
    
})(jQuery);
