<?php
/**
 * Firebase Authentication Handler
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Firebase Authentication class
 */
class Smart_Auth_Firebase {

    /**
     * Firebase project settings
     *
     * @var array
     */
    private $settings;

    /**
     * Firebase REST API base URL
     *
     * @var string
     */
    private $api_base_url = 'https://identitytoolkit.googleapis.com/v1';

    /**
     * Firebase Auth base URL
     *
     * @var string
     */
    private $auth_base_url = 'https://www.googleapis.com/identitytoolkit/v3/relyingparty';

    /**
     * HTTP client
     *
     * @var GuzzleHttp\Client
     */
    private $http_client;

    /**
     * Constructor
     */
    public function __construct() {
        $this->settings = get_option('smart_auth_firebase_settings', array());

        // Initialize HTTP client if Guzzle is available
        if (class_exists('GuzzleHttp\Client')) {
            $this->http_client = new \GuzzleHttp\Client([
                'timeout' => 30,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'Smart-Auth-WordPress-Plugin/1.0.0',
                ],
            ]);
        }
    }

    /**
     * Verify Firebase ID token
     *
     * @param string $id_token Firebase ID token
     * @return array|WP_Error User data or error
     */
    public function verify_id_token($id_token) {
        if (empty($this->settings['api_key'])) {
            return new WP_Error('firebase_not_configured', __('Firebase is not configured.', 'smart-auth'));
        }

        if (empty($id_token)) {
            return new WP_Error('invalid_token', __('Invalid ID token provided.', 'smart-auth'));
        }

        try {
            // Use Firebase JWT library if available, otherwise use REST API
            if (class_exists('Firebase\JWT\JWT')) {
                return $this->verify_token_with_jwt($id_token);
            } else {
                return $this->verify_token_with_api($id_token);
            }
        } catch (Exception $e) {
            error_log('Smart Auth Firebase Error: ' . $e->getMessage());
            return new WP_Error('firebase_error', __('Failed to verify Firebase token.', 'smart-auth'));
        }
    }

    /**
     * Verify token using Firebase JWT library
     *
     * @param string $id_token Firebase ID token
     * @return array|WP_Error User data or error
     */
    private function verify_token_with_jwt($id_token) {
        try {
            // Get Firebase public keys
            $keys = $this->get_firebase_public_keys();
            if (is_wp_error($keys)) {
                return $keys;
            }

            // Decode and verify token
            $decoded = \Firebase\JWT\JWT::decode($id_token, $keys, ['RS256']);

            // Validate token claims
            $validation_result = $this->validate_token_claims($decoded);
            if (is_wp_error($validation_result)) {
                return $validation_result;
            }

            // Convert to user data format
            return $this->format_user_data($decoded);

        } catch (\Firebase\JWT\ExpiredException $e) {
            return new WP_Error('token_expired', __('Firebase token has expired.', 'smart-auth'));
        } catch (\Firebase\JWT\InvalidTokenException $e) {
            return new WP_Error('invalid_token', __('Invalid Firebase token.', 'smart-auth'));
        } catch (Exception $e) {
            error_log('Smart Auth JWT Error: ' . $e->getMessage());
            return new WP_Error('jwt_error', __('Failed to verify token with JWT.', 'smart-auth'));
        }
    }

    /**
     * Verify token using Firebase REST API
     *
     * @param string $id_token Firebase ID token
     * @return array|WP_Error User data or error
     */
    private function verify_token_with_api($id_token) {
        if (!$this->http_client) {
            return new WP_Error('http_client_missing', __('HTTP client not available.', 'smart-auth'));
        }

        try {
            $response = $this->http_client->post($this->auth_base_url . '/getAccountInfo', [
                'json' => [
                    'idToken' => $id_token,
                ],
                'query' => [
                    'key' => $this->settings['api_key'],
                ],
            ]);

            $body = json_decode($response->getBody()->getContents(), true);

            if (isset($body['error'])) {
                return new WP_Error('firebase_api_error', $body['error']['message']);
            }

            if (empty($body['users']) || !is_array($body['users'])) {
                return new WP_Error('invalid_response', __('Invalid response from Firebase.', 'smart-auth'));
            }

            $user_data = $body['users'][0];
            return $this->format_api_user_data($user_data);

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            error_log('Smart Auth Firebase API Error: ' . $e->getMessage());
            return new WP_Error('firebase_request_error', __('Failed to verify token with Firebase API.', 'smart-auth'));
        }
    }

    /**
     * Get Firebase public keys for JWT verification
     *
     * @return array|WP_Error Public keys or error
     */
    private function get_firebase_public_keys() {
        $cache_key = 'smart_auth_firebase_keys';
        $keys = get_transient($cache_key);

        if ($keys !== false) {
            return $keys;
        }

        if (!$this->http_client) {
            return new WP_Error('http_client_missing', __('HTTP client not available.', 'smart-auth'));
        }

        try {
            $response = $this->http_client->get('https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>');
            $keys_data = json_decode($response->getBody()->getContents(), true);

            if (!is_array($keys_data)) {
                return new WP_Error('invalid_keys', __('Invalid public keys received from Firebase.', 'smart-auth'));
            }

            // Convert to Firebase JWT format
            $formatted_keys = array();
            foreach ($keys_data as $kid => $key) {
                $formatted_keys[$kid] = new \Firebase\JWT\Key($key, 'RS256');
            }

            // Cache for 1 hour
            set_transient($cache_key, $formatted_keys, HOUR_IN_SECONDS);

            return $formatted_keys;

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            error_log('Smart Auth Firebase Keys Error: ' . $e->getMessage());
            return new WP_Error('keys_fetch_error', __('Failed to fetch Firebase public keys.', 'smart-auth'));
        }
    }

    /**
     * Validate Firebase token claims
     *
     * @param object $decoded Decoded JWT token
     * @return bool|WP_Error True if valid, error otherwise
     */
    private function validate_token_claims($decoded) {
        // Check issuer
        $expected_issuer = 'https://securetoken.google.com/' . $this->settings['project_id'];
        if ($decoded->iss !== $expected_issuer) {
            return new WP_Error('invalid_issuer', __('Invalid token issuer.', 'smart-auth'));
        }

        // Check audience
        if ($decoded->aud !== $this->settings['project_id']) {
            return new WP_Error('invalid_audience', __('Invalid token audience.', 'smart-auth'));
        }

        // Check expiration
        if ($decoded->exp < time()) {
            return new WP_Error('token_expired', __('Token has expired.', 'smart-auth'));
        }

        // Check issued at time
        if ($decoded->iat > time()) {
            return new WP_Error('token_future', __('Token issued in the future.', 'smart-auth'));
        }

        // Check auth time
        if (isset($decoded->auth_time) && $decoded->auth_time > time()) {
            return new WP_Error('auth_time_future', __('Auth time is in the future.', 'smart-auth'));
        }

        return true;
    }

    /**
     * Format user data from JWT token
     *
     * @param object $decoded Decoded JWT token
     * @return array Formatted user data
     */
    private function format_user_data($decoded) {
        $user_data = array(
            'firebase_uid' => $decoded->sub,
            'email' => isset($decoded->email) ? $decoded->email : '',
            'email_verified' => isset($decoded->email_verified) ? $decoded->email_verified : false,
            'name' => isset($decoded->name) ? $decoded->name : '',
            'picture' => isset($decoded->picture) ? $decoded->picture : '',
            'phone_number' => isset($decoded->phone_number) ? $decoded->phone_number : '',
            'provider_id' => isset($decoded->firebase->identities) ? $this->extract_provider_id($decoded->firebase->identities) : '',
            'custom_claims' => isset($decoded->custom_claims) ? $decoded->custom_claims : array(),
        );

        return $user_data;
    }

    /**
     * Format user data from API response
     *
     * @param array $user_data User data from Firebase API
     * @return array Formatted user data
     */
    private function format_api_user_data($user_data) {
        $formatted_data = array(
            'firebase_uid' => isset($user_data['localId']) ? $user_data['localId'] : '',
            'email' => isset($user_data['email']) ? $user_data['email'] : '',
            'email_verified' => isset($user_data['emailVerified']) ? $user_data['emailVerified'] : false,
            'name' => isset($user_data['displayName']) ? $user_data['displayName'] : '',
            'picture' => isset($user_data['photoUrl']) ? $user_data['photoUrl'] : '',
            'phone_number' => isset($user_data['phoneNumber']) ? $user_data['phoneNumber'] : '',
            'provider_id' => '',
            'custom_claims' => array(),
        );

        // Extract provider ID from provider user info
        if (isset($user_data['providerUserInfo']) && is_array($user_data['providerUserInfo'])) {
            foreach ($user_data['providerUserInfo'] as $provider) {
                if (isset($provider['providerId'])) {
                    $formatted_data['provider_id'] = $this->normalize_provider_id($provider['providerId']);
                    break;
                }
            }
        }

        return $formatted_data;
    }

    /**
     * Extract provider ID from Firebase identities
     *
     * @param object $identities Firebase identities object
     * @return string Provider ID
     */
    private function extract_provider_id($identities) {
        if (isset($identities->{'google.com'})) {
            return 'google';
        } elseif (isset($identities->{'facebook.com'})) {
            return 'facebook';
        } elseif (isset($identities->{'apple.com'})) {
            return 'apple';
        } elseif (isset($identities->phone)) {
            return 'phone';
        }

        return 'unknown';
    }

    /**
     * Normalize provider ID
     *
     * @param string $provider_id Raw provider ID
     * @return string Normalized provider ID
     */
    private function normalize_provider_id($provider_id) {
        switch ($provider_id) {
            case 'google.com':
                return 'google';
            case 'facebook.com':
                return 'facebook';
            case 'apple.com':
                return 'apple';
            case 'phone':
                return 'phone';
            default:
                return 'unknown';
        }
    }

    /**
     * Send OTP via Firebase Auth
     *
     * @param string $phone_number Phone number in E.164 format
     * @return array|WP_Error Response or error
     */
    public function send_phone_otp($phone_number) {
        if (empty($this->settings['api_key'])) {
            return new WP_Error('firebase_not_configured', __('Firebase is not configured.', 'smart-auth'));
        }

        if (!$this->http_client) {
            return new WP_Error('http_client_missing', __('HTTP client not available.', 'smart-auth'));
        }

        // Validate phone number format
        if (!$this->is_valid_phone_number($phone_number)) {
            return new WP_Error('invalid_phone', __('Invalid phone number format.', 'smart-auth'));
        }

        try {
            $response = $this->http_client->post($this->auth_base_url . '/sendVerificationCode', [
                'json' => [
                    'phoneNumber' => $phone_number,
                    'recaptchaToken' => '', // For server-side, this can be empty
                ],
                'query' => [
                    'key' => $this->settings['api_key'],
                ],
            ]);

            $body = json_decode($response->getBody()->getContents(), true);

            if (isset($body['error'])) {
                return new WP_Error('firebase_otp_error', $body['error']['message']);
            }

            if (!isset($body['sessionInfo'])) {
                return new WP_Error('invalid_response', __('Invalid response from Firebase.', 'smart-auth'));
            }

            return array(
                'session_info' => $body['sessionInfo'],
                'phone_number' => $phone_number,
            );

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            error_log('Smart Auth Firebase OTP Error: ' . $e->getMessage());
            return new WP_Error('firebase_request_error', __('Failed to send OTP via Firebase.', 'smart-auth'));
        }
    }

    /**
     * Verify phone OTP
     *
     * @param string $session_info Session info from send_phone_otp
     * @param string $otp_code OTP code
     * @return array|WP_Error User data or error
     */
    public function verify_phone_otp($session_info, $otp_code) {
        if (empty($this->settings['api_key'])) {
            return new WP_Error('firebase_not_configured', __('Firebase is not configured.', 'smart-auth'));
        }

        if (!$this->http_client) {
            return new WP_Error('http_client_missing', __('HTTP client not available.', 'smart-auth'));
        }

        if (empty($session_info) || empty($otp_code)) {
            return new WP_Error('missing_parameters', __('Session info and OTP code are required.', 'smart-auth'));
        }

        try {
            $response = $this->http_client->post($this->auth_base_url . '/verifyPhoneNumber', [
                'json' => [
                    'sessionInfo' => $session_info,
                    'code' => $otp_code,
                ],
                'query' => [
                    'key' => $this->settings['api_key'],
                ],
            ]);

            $body = json_decode($response->getBody()->getContents(), true);

            if (isset($body['error'])) {
                return new WP_Error('firebase_verify_error', $body['error']['message']);
            }

            if (!isset($body['idToken'])) {
                return new WP_Error('invalid_response', __('Invalid response from Firebase.', 'smart-auth'));
            }

            // Verify the returned ID token to get user data
            return $this->verify_id_token($body['idToken']);

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            error_log('Smart Auth Firebase Verify Error: ' . $e->getMessage());
            return new WP_Error('firebase_request_error', __('Failed to verify OTP with Firebase.', 'smart-auth'));
        }
    }

    /**
     * Test Firebase connection
     *
     * @return bool|WP_Error True if connection successful, error otherwise
     */
    public function test_connection() {
        if (empty($this->settings['api_key']) || empty($this->settings['project_id'])) {
            return new WP_Error('incomplete_config', __('Firebase configuration is incomplete.', 'smart-auth'));
        }

        if (!$this->http_client) {
            return new WP_Error('http_client_missing', __('HTTP client not available.', 'smart-auth'));
        }

        try {
            // Test by trying to get project configuration
            $response = $this->http_client->get($this->api_base_url . '/projects/' . $this->settings['project_id'] . '/config', [
                'query' => [
                    'key' => $this->settings['api_key'],
                ],
            ]);

            $body = json_decode($response->getBody()->getContents(), true);

            if (isset($body['error'])) {
                return new WP_Error('firebase_config_error', $body['error']['message']);
            }

            // Check if the response contains expected project data
            if (!isset($body['projectId']) || $body['projectId'] !== $this->settings['project_id']) {
                return new WP_Error('project_mismatch', __('Project ID mismatch in Firebase response.', 'smart-auth'));
            }

            return true;

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            error_log('Smart Auth Firebase Test Error: ' . $e->getMessage());
            return new WP_Error('firebase_connection_error', __('Failed to connect to Firebase.', 'smart-auth'));
        }
    }

    /**
     * Validate phone number format
     *
     * @param string $phone_number Phone number to validate
     * @return bool True if valid, false otherwise
     */
    private function is_valid_phone_number($phone_number) {
        // Basic E.164 format validation
        return preg_match('/^\+[1-9]\d{1,14}$/', $phone_number);
    }

    /**
     * Get Firebase settings
     *
     * @return array Firebase settings
     */
    public function get_settings() {
        return $this->settings;
    }

    /**
     * Update Firebase settings
     *
     * @param array $settings New settings
     * @return bool True on success, false on failure
     */
    public function update_settings($settings) {
        $this->settings = $settings;
        return update_option('smart_auth_firebase_settings', $settings);
    }
}
