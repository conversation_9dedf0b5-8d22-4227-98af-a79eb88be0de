<?php
/**
 * Bricks Auth Form Element
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Smart Auth Form Element for Bricks Builder
 */
class Smart_Auth_Bricks_Auth_Form_Element extends \Bricks\Element {
    
    /**
     * Element category
     *
     * @var string
     */
    public $category = 'general';
    
    /**
     * Element name
     *
     * @var string
     */
    public $name = 'smart-auth-form';
    
    /**
     * Element icon
     *
     * @var string
     */
    public $icon = 'ti-lock';
    
    /**
     * Element CSS selector
     *
     * @var string
     */
    public $css_selector = '.smart-auth-form-wrapper';
    
    /**
     * Element scripts
     *
     * @var array
     */
    public $scripts = array('smartAuthForm');
    
    /**
     * Element is nestable
     *
     * @var bool
     */
    public $nestable = false;
    
    /**
     * Get element label
     *
     * @return string Element label
     */
    public function get_label() {
        return __('Smart Auth Form', 'smart-auth');
    }
    
    /**
     * Get element keywords
     *
     * @return array Element keywords
     */
    public function get_keywords() {
        return array('auth', 'login', 'register', 'firebase', 'authentication');
    }
    
    /**
     * Set control groups
     */
    public function set_control_groups() {
        $this->control_groups['content'] = array(
            'title' => __('Content', 'smart-auth'),
            'tab' => 'content',
        );
        
        $this->control_groups['providers'] = array(
            'title' => __('Providers', 'smart-auth'),
            'tab' => 'content',
        );
        
        $this->control_groups['settings'] = array(
            'title' => __('Settings', 'smart-auth'),
            'tab' => 'content',
        );
    }
    
    /**
     * Set controls
     */
    public function set_controls() {
        // Content controls
        $this->controls['form_title'] = array(
            'tab' => 'content',
            'group' => 'content',
            'label' => __('Form Title', 'smart-auth'),
            'type' => 'text',
            'default' => __('Sign In', 'smart-auth'),
        );
        
        $this->controls['form_description'] = array(
            'tab' => 'content',
            'group' => 'content',
            'label' => __('Form Description', 'smart-auth'),
            'type' => 'textarea',
            'default' => '',
        );
        
        // Provider controls
        $this->controls['enable_google'] = array(
            'tab' => 'content',
            'group' => 'providers',
            'label' => __('Enable Google', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
        
        $this->controls['enable_facebook'] = array(
            'tab' => 'content',
            'group' => 'providers',
            'label' => __('Enable Facebook', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
        
        $this->controls['enable_apple'] = array(
            'tab' => 'content',
            'group' => 'providers',
            'label' => __('Enable Apple', 'smart-auth'),
            'type' => 'checkbox',
            'default' => false,
        );
        
        $this->controls['enable_phone'] = array(
            'tab' => 'content',
            'group' => 'providers',
            'label' => __('Enable Phone', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
        
        // Settings controls
        $this->controls['form_style'] = array(
            'tab' => 'content',
            'group' => 'settings',
            'label' => __('Form Style', 'smart-auth'),
            'type' => 'select',
            'options' => array(
                'default' => __('Default', 'smart-auth'),
                'minimal' => __('Minimal', 'smart-auth'),
                'modern' => __('Modern', 'smart-auth'),
            ),
            'default' => 'default',
        );
        
        $this->controls['redirect_url'] = array(
            'tab' => 'content',
            'group' => 'settings',
            'label' => __('Redirect URL', 'smart-auth'),
            'type' => 'text',
            'default' => '',
            'placeholder' => 'https://your-domain.com',
        );
        
        $this->controls['show_labels'] = array(
            'tab' => 'content',
            'group' => 'settings',
            'label' => __('Show Labels', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
    }
    
    /**
     * Enqueue element scripts
     */
    public function enqueue_scripts() {
        wp_enqueue_script(
            'smart-auth-bricks-form',
            SMART_AUTH_PLUGIN_URL . 'assets/js/bricks-form.js',
            array('jquery'),
            SMART_AUTH_VERSION,
            true
        );
    }
    
    /**
     * Render element
     */
    public function render() {
        $settings = $this->settings;
        
        // Get form settings
        $form_title = isset($settings['form_title']) ? $settings['form_title'] : __('Sign In', 'smart-auth');
        $form_description = isset($settings['form_description']) ? $settings['form_description'] : '';
        $form_style = isset($settings['form_style']) ? $settings['form_style'] : 'default';
        $redirect_url = isset($settings['redirect_url']) ? $settings['redirect_url'] : '';
        $show_labels = isset($settings['show_labels']) ? $settings['show_labels'] : true;
        
        // Build enabled providers array
        $enabled_providers = array();
        if (isset($settings['enable_google']) && $settings['enable_google']) {
            $enabled_providers[] = 'google';
        }
        if (isset($settings['enable_facebook']) && $settings['enable_facebook']) {
            $enabled_providers[] = 'facebook';
        }
        if (isset($settings['enable_apple']) && $settings['enable_apple']) {
            $enabled_providers[] = 'apple';
        }
        if (isset($settings['enable_phone']) && $settings['enable_phone']) {
            $enabled_providers[] = 'phone';
        }
        
        // Set wrapper attributes
        $this->set_attribute('wrapper', 'class', 'smart-auth-form-wrapper');
        $this->set_attribute('wrapper', 'class', 'smart-auth-form-' . $form_style);
        
        // Build shortcode attributes
        $shortcode_atts = array(
            'title="' . esc_attr($form_title) . '"',
            'show_providers="' . esc_attr(implode(',', $enabled_providers)) . '"',
            'form_style="' . esc_attr($form_style) . '"',
        );
        
        if (!empty($form_description)) {
            $shortcode_atts[] = 'description="' . esc_attr($form_description) . '"';
        }
        
        if (!empty($redirect_url)) {
            $shortcode_atts[] = 'redirect_url="' . esc_url($redirect_url) . '"';
        }
        
        if (!$show_labels) {
            $shortcode_atts[] = 'show_labels="false"';
        }
        
        // Render element
        echo "<div {$this->render_attributes('wrapper')}>";
        echo do_shortcode('[smart_auth_form ' . implode(' ', $shortcode_atts) . ']');
        echo '</div>';
    }
}
