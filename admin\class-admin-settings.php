<?php
/**
 * Admin Settings Handler
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin Settings class
 */
class Smart_Auth_Admin_Settings {
    
    /**
     * Settings page hook suffix
     *
     * @var string
     */
    private $page_hook;
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('wp_ajax_smart_auth_test_firebase', array($this, 'ajax_test_firebase'));
        add_action('wp_ajax_smart_auth_test_twilio', array($this, 'ajax_test_twilio'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        $this->page_hook = add_options_page(
            __('Smart Auth Settings', 'smart-auth'),
            __('Smart Auth', 'smart-auth'),
            'manage_options',
            'smart-auth-settings',
            array($this, 'render_settings_page')
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        // Firebase settings
        register_setting('smart_auth_firebase', 'smart_auth_firebase_settings', array(
            'sanitize_callback' => array($this, 'sanitize_firebase_settings'),
        ));
        
        // Twilio settings
        register_setting('smart_auth_twilio', 'smart_auth_twilio_settings', array(
            'sanitize_callback' => array($this, 'sanitize_twilio_settings'),
        ));
        
        // JWT settings
        register_setting('smart_auth_jwt', 'smart_auth_jwt_settings', array(
            'sanitize_callback' => array($this, 'sanitize_jwt_settings'),
        ));
        
        // User settings
        register_setting('smart_auth_user', 'smart_auth_user_settings', array(
            'sanitize_callback' => array($this, 'sanitize_user_settings'),
        ));
        
        // Security settings
        register_setting('smart_auth_security', 'smart_auth_security_settings', array(
            'sanitize_callback' => array($this, 'sanitize_security_settings'),
        ));
    }
    
    /**
     * Render settings page
     */
    public function render_settings_page() {
        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'firebase';
        ?>
        <div class="wrap">
            <h1><?php echo esc_html__('Smart Auth Settings', 'smart-auth'); ?></h1>
            
            <nav class="nav-tab-wrapper">
                <a href="?page=smart-auth-settings&tab=firebase" class="nav-tab <?php echo $active_tab === 'firebase' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('Firebase', 'smart-auth'); ?>
                </a>
                <a href="?page=smart-auth-settings&tab=twilio" class="nav-tab <?php echo $active_tab === 'twilio' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('Twilio', 'smart-auth'); ?>
                </a>
                <a href="?page=smart-auth-settings&tab=jwt" class="nav-tab <?php echo $active_tab === 'jwt' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('JWT', 'smart-auth'); ?>
                </a>
                <a href="?page=smart-auth-settings&tab=user" class="nav-tab <?php echo $active_tab === 'user' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('User Settings', 'smart-auth'); ?>
                </a>
                <a href="?page=smart-auth-settings&tab=security" class="nav-tab <?php echo $active_tab === 'security' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('Security', 'smart-auth'); ?>
                </a>
            </nav>
            
            <div class="tab-content">
                <?php
                switch ($active_tab) {
                    case 'firebase':
                        $this->render_firebase_tab();
                        break;
                    case 'twilio':
                        $this->render_twilio_tab();
                        break;
                    case 'jwt':
                        $this->render_jwt_tab();
                        break;
                    case 'user':
                        $this->render_user_tab();
                        break;
                    case 'security':
                        $this->render_security_tab();
                        break;
                    default:
                        $this->render_firebase_tab();
                        break;
                }
                ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render Firebase settings tab
     */
    private function render_firebase_tab() {
        $settings = get_option('smart_auth_firebase_settings', array());
        ?>
        <form method="post" action="options.php">
            <?php
            settings_fields('smart_auth_firebase');
            do_settings_sections('smart_auth_firebase');
            ?>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php esc_html_e('Project ID', 'smart-auth'); ?></th>
                    <td>
                        <input type="text" name="smart_auth_firebase_settings[project_id]" 
                               value="<?php echo esc_attr(isset($settings['project_id']) ? $settings['project_id'] : ''); ?>" 
                               class="regular-text" />
                        <p class="description"><?php esc_html_e('Your Firebase project ID', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('API Key', 'smart-auth'); ?></th>
                    <td>
                        <input type="text" name="smart_auth_firebase_settings[api_key]" 
                               value="<?php echo esc_attr(isset($settings['api_key']) ? $settings['api_key'] : ''); ?>" 
                               class="regular-text" />
                        <p class="description"><?php esc_html_e('Your Firebase Web API key', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Auth Domain', 'smart-auth'); ?></th>
                    <td>
                        <input type="text" name="smart_auth_firebase_settings[auth_domain]" 
                               value="<?php echo esc_attr(isset($settings['auth_domain']) ? $settings['auth_domain'] : ''); ?>" 
                               class="regular-text" />
                        <p class="description"><?php esc_html_e('Your Firebase auth domain (e.g., project-id.firebaseapp.com)', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Storage Bucket', 'smart-auth'); ?></th>
                    <td>
                        <input type="text" name="smart_auth_firebase_settings[storage_bucket]" 
                               value="<?php echo esc_attr(isset($settings['storage_bucket']) ? $settings['storage_bucket'] : ''); ?>" 
                               class="regular-text" />
                        <p class="description"><?php esc_html_e('Your Firebase storage bucket', 'smart-auth'); ?></p>
                    </td>
                </tr>
            </table>
            
            <p>
                <button type="button" id="test-firebase-connection" class="button button-secondary">
                    <?php esc_html_e('Test Connection', 'smart-auth'); ?>
                </button>
                <span id="firebase-test-result"></span>
            </p>
            
            <?php submit_button(); ?>
        </form>
        <?php
    }
    
    /**
     * Render Twilio settings tab
     */
    private function render_twilio_tab() {
        // Implementation will be added in the next task
        echo '<p>' . esc_html__('Twilio settings tab - Implementation pending', 'smart-auth') . '</p>';
    }
    
    /**
     * Render JWT settings tab
     */
    private function render_jwt_tab() {
        // Implementation will be added in the next task
        echo '<p>' . esc_html__('JWT settings tab - Implementation pending', 'smart-auth') . '</p>';
    }
    
    /**
     * Render User settings tab
     */
    private function render_user_tab() {
        // Implementation will be added in the next task
        echo '<p>' . esc_html__('User settings tab - Implementation pending', 'smart-auth') . '</p>';
    }
    
    /**
     * Render Security settings tab
     */
    private function render_security_tab() {
        // Implementation will be added in the next task
        echo '<p>' . esc_html__('Security settings tab - Implementation pending', 'smart-auth') . '</p>';
    }
    
    /**
     * Sanitize Firebase settings
     */
    public function sanitize_firebase_settings($input) {
        $sanitized = array();
        
        if (isset($input['project_id'])) {
            $sanitized['project_id'] = sanitize_text_field($input['project_id']);
        }
        
        if (isset($input['api_key'])) {
            $sanitized['api_key'] = sanitize_text_field($input['api_key']);
        }
        
        if (isset($input['auth_domain'])) {
            $sanitized['auth_domain'] = sanitize_text_field($input['auth_domain']);
        }
        
        if (isset($input['storage_bucket'])) {
            $sanitized['storage_bucket'] = sanitize_text_field($input['storage_bucket']);
        }
        
        return $sanitized;
    }
    
    // Placeholder sanitization methods
    public function sanitize_twilio_settings($input) { return $input; }
    public function sanitize_jwt_settings($input) { return $input; }
    public function sanitize_user_settings($input) { return $input; }
    public function sanitize_security_settings($input) { return $input; }
    
    // Placeholder AJAX methods
    public function ajax_test_firebase() { wp_die('Not implemented yet'); }
    public function ajax_test_twilio() { wp_die('Not implemented yet'); }
}
