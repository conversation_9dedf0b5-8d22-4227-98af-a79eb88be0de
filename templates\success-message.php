<?php
/**
 * Success Message Template
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Default attributes
$defaults = array(
    'title' => __('Authentication Successful!', 'smart-auth'),
    'message' => __('You have been successfully authenticated.', 'smart-auth'),
    'show_user_info' => true,
    'show_redirect_info' => true,
    'redirect_url' => '',
    'redirect_delay' => 3,
    'css_class' => '',
);

$atts = wp_parse_args($atts, $defaults);

// Sanitize attributes
$title = sanitize_text_field($atts['title']);
$message = sanitize_textarea_field($atts['message']);
$show_user_info = filter_var($atts['show_user_info'], FILTER_VALIDATE_BOOLEAN);
$show_redirect_info = filter_var($atts['show_redirect_info'], FILTER_VALIDATE_BOOLEAN);
$redirect_url = esc_url($atts['redirect_url']);
$redirect_delay = (int) $atts['redirect_delay'];
$css_class = sanitize_html_class($atts['css_class']);

// Get current user
$current_user = wp_get_current_user();

// Build CSS classes
$wrapper_classes = array(
    'smart-auth-success-message',
);

if (!empty($css_class)) {
    $wrapper_classes[] = $css_class;
}

$wrapper_class = implode(' ', $wrapper_classes);

// Determine redirect URL
if (empty($redirect_url)) {
    $redirect_url = apply_filters('smart_auth_default_redirect_url', home_url(), $current_user);
}
?>

<div class="<?php echo esc_attr($wrapper_class); ?>" 
     data-redirect-url="<?php echo esc_attr($redirect_url); ?>"
     data-redirect-delay="<?php echo esc_attr($redirect_delay); ?>">
    
    <div class="smart-auth-success-icon">
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" fill="#28a745"/>
            <path d="M9 12l2 2 4-4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
    </div>
    
    <h2 class="smart-auth-success-title"><?php echo esc_html($title); ?></h2>
    
    <p class="smart-auth-success-message"><?php echo esc_html($message); ?></p>
    
    <?php if ($show_user_info && $current_user && $current_user->ID > 0) : ?>
        <div class="smart-auth-user-info">
            <div class="smart-auth-user-avatar">
                <?php echo get_avatar($current_user->ID, 64); ?>
            </div>
            <div class="smart-auth-user-details">
                <h3><?php echo esc_html($current_user->display_name); ?></h3>
                <p><?php echo esc_html($current_user->user_email); ?></p>
                
                <?php
                // Show authentication provider if available
                $auth_provider = get_user_meta($current_user->ID, 'auth_provider', true);
                if (!empty($auth_provider)) :
                ?>
                    <p class="smart-auth-provider">
                        <?php printf(esc_html__('Authenticated via %s', 'smart-auth'), '<strong>' . esc_html(ucfirst($auth_provider)) . '</strong>'); ?>
                    </p>
                <?php endif; ?>
                
                <?php
                // Show phone number if available
                $phone_number = get_user_meta($current_user->ID, 'phone_number', true);
                if (!empty($phone_number)) :
                ?>
                    <p class="smart-auth-phone">
                        <?php printf(esc_html__('Phone: %s', 'smart-auth'), '<strong>' . esc_html($phone_number) . '</strong>'); ?>
                    </p>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
    
    <?php if ($show_redirect_info && !empty($redirect_url)) : ?>
        <div class="smart-auth-redirect-info">
            <p>
                <?php printf(
                    esc_html__('You will be redirected in %s seconds...', 'smart-auth'),
                    '<span class="smart-auth-countdown">' . $redirect_delay . '</span>'
                ); ?>
            </p>
            <p>
                <a href="<?php echo esc_url($redirect_url); ?>" class="smart-auth-redirect-link">
                    <?php esc_html_e('Click here if you are not redirected automatically', 'smart-auth'); ?>
                </a>
            </p>
        </div>
    <?php endif; ?>
    
    <div class="smart-auth-actions">
        <?php if (!empty($redirect_url)) : ?>
            <a href="<?php echo esc_url($redirect_url); ?>" class="smart-auth-button smart-auth-continue-button">
                <?php esc_html_e('Continue', 'smart-auth'); ?>
            </a>
        <?php endif; ?>
        
        <a href="<?php echo esc_url(wp_logout_url()); ?>" class="smart-auth-button smart-auth-logout-button">
            <?php esc_html_e('Logout', 'smart-auth'); ?>
        </a>
    </div>
</div>

<?php
// Add inline styles
?>
<style>
.smart-auth-success-message {
    max-width: 500px;
    margin: 0 auto;
    padding: 40px 20px;
    text-align: center;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.smart-auth-success-icon {
    margin-bottom: 20px;
}

.smart-auth-success-title {
    margin: 0 0 16px 0;
    font-size: 24px;
    font-weight: 600;
    color: #28a745;
}

.smart-auth-success-message p {
    margin: 0 0 20px 0;
    color: #666;
    font-size: 16px;
    line-height: 1.5;
}

.smart-auth-user-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.smart-auth-user-avatar img {
    border-radius: 50%;
}

.smart-auth-user-details {
    text-align: left;
}

.smart-auth-user-details h3 {
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.smart-auth-user-details p {
    margin: 0 0 4px 0;
    font-size: 14px;
    color: #666;
}

.smart-auth-provider,
.smart-auth-phone {
    font-size: 12px !important;
    color: #999 !important;
}

.smart-auth-redirect-info {
    margin: 20px 0;
    padding: 16px;
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
}

.smart-auth-redirect-info p {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #0066cc;
}

.smart-auth-redirect-info p:last-child {
    margin-bottom: 0;
}

.smart-auth-countdown {
    font-weight: 600;
    color: #0066cc;
}

.smart-auth-redirect-link {
    color: #0066cc;
    text-decoration: underline;
}

.smart-auth-redirect-link:hover {
    text-decoration: none;
}

.smart-auth-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 30px;
}

.smart-auth-continue-button {
    background: #28a745;
    border-color: #28a745;
    color: #fff;
}

.smart-auth-continue-button:hover {
    background: #218838;
    border-color: #218838;
    color: #fff;
}

.smart-auth-logout-button {
    background: #6c757d;
    border-color: #6c757d;
    color: #fff;
}

.smart-auth-logout-button:hover {
    background: #5a6268;
    border-color: #5a6268;
    color: #fff;
}

@media (max-width: 480px) {
    .smart-auth-success-message {
        margin: 0 16px;
        padding: 30px 16px;
    }
    
    .smart-auth-user-info {
        flex-direction: column;
        text-align: center;
    }
    
    .smart-auth-user-details {
        text-align: center;
    }
    
    .smart-auth-actions {
        flex-direction: column;
    }
}
</style>

<?php
// Add JavaScript for countdown and redirect
?>
<script>
(function() {
    'use strict';
    
    var successMessage = document.querySelector('.smart-auth-success-message');
    if (!successMessage) return;
    
    var redirectUrl = successMessage.getAttribute('data-redirect-url');
    var redirectDelay = parseInt(successMessage.getAttribute('data-redirect-delay')) || 3;
    var countdownElement = successMessage.querySelector('.smart-auth-countdown');
    
    if (redirectUrl && countdownElement) {
        var countdown = redirectDelay;
        
        var timer = setInterval(function() {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = redirectUrl;
            }
        }, 1000);
    }
})();
</script>
