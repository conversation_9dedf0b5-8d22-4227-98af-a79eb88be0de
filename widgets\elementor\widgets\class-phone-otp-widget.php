<?php
/**
 * Elementor Phone OTP Widget
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Phone OTP Verification Widget for Elementor
 */
class Smart_Auth_Elementor_Phone_OTP_Widget extends \Elementor\Widget_Base {
    
    /**
     * Get widget name
     *
     * @return string Widget name
     */
    public function get_name() {
        return 'smart-auth-phone-otp';
    }
    
    /**
     * Get widget title
     *
     * @return string Widget title
     */
    public function get_title() {
        return __('Phone OTP Form', 'smart-auth');
    }
    
    /**
     * Get widget icon
     *
     * @return string Widget icon
     */
    public function get_icon() {
        return 'eicon-call-to-action';
    }
    
    /**
     * Get widget categories
     *
     * @return array Widget categories
     */
    public function get_categories() {
        return array('smart-auth');
    }
    
    /**
     * Get widget keywords
     *
     * @return array Widget keywords
     */
    public function get_keywords() {
        return array('phone', 'otp', 'sms', 'verification', 'authentication');
    }
    
    /**
     * Register widget controls
     */
    protected function register_controls() {
        // Content Section
        $this->start_controls_section(
            'content_section',
            array(
                'label' => __('Content', 'smart-auth'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            )
        );
        
        $this->add_control(
            'form_title',
            array(
                'label' => __('Form Title', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Phone Verification', 'smart-auth'),
                'placeholder' => __('Enter form title', 'smart-auth'),
            )
        );
        
        $this->add_control(
            'form_description',
            array(
                'label' => __('Form Description', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::TEXTAREA,
                'default' => __('Enter your phone number to receive a verification code.', 'smart-auth'),
                'placeholder' => __('Enter form description', 'smart-auth'),
            )
        );
        
        $this->add_control(
            'phone_placeholder',
            array(
                'label' => __('Phone Placeholder', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Enter your phone number', 'smart-auth'),
                'placeholder' => __('Phone input placeholder', 'smart-auth'),
            )
        );
        
        $this->add_control(
            'otp_placeholder',
            array(
                'label' => __('OTP Placeholder', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Enter verification code', 'smart-auth'),
                'placeholder' => __('OTP input placeholder', 'smart-auth'),
            )
        );
        
        $this->end_controls_section();
        
        // Settings Section
        $this->start_controls_section(
            'settings_section',
            array(
                'label' => __('Settings', 'smart-auth'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            )
        );
        
        $this->add_control(
            'otp_provider',
            array(
                'label' => __('OTP Provider', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'options' => array(
                    'firebase' => __('Firebase', 'smart-auth'),
                    'twilio' => __('Twilio', 'smart-auth'),
                ),
                'default' => 'firebase',
            )
        );
        
        $this->add_control(
            'default_country',
            array(
                'label' => __('Default Country', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'options' => array(
                    'US' => __('United States', 'smart-auth'),
                    'CA' => __('Canada', 'smart-auth'),
                    'GB' => __('United Kingdom', 'smart-auth'),
                    'AU' => __('Australia', 'smart-auth'),
                    'DE' => __('Germany', 'smart-auth'),
                    'FR' => __('France', 'smart-auth'),
                    'IT' => __('Italy', 'smart-auth'),
                    'ES' => __('Spain', 'smart-auth'),
                    'BR' => __('Brazil', 'smart-auth'),
                    'IN' => __('India', 'smart-auth'),
                    'JP' => __('Japan', 'smart-auth'),
                ),
                'default' => 'US',
            )
        );
        
        $this->add_control(
            'show_country_selector',
            array(
                'label' => __('Show Country Selector', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'smart-auth'),
                'label_off' => __('Hide', 'smart-auth'),
                'return_value' => 'yes',
                'default' => 'yes',
            )
        );
        
        $this->add_control(
            'redirect_url',
            array(
                'label' => __('Redirect URL', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::URL,
                'placeholder' => __('https://your-domain.com', 'smart-auth'),
                'show_external' => true,
                'default' => array(
                    'url' => '',
                    'is_external' => false,
                    'nofollow' => false,
                ),
            )
        );
        
        $this->add_control(
            'enable_resend',
            array(
                'label' => __('Enable Resend', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'smart-auth'),
                'label_off' => __('No', 'smart-auth'),
                'return_value' => 'yes',
                'default' => 'yes',
            )
        );
        
        $this->add_control(
            'resend_cooldown',
            array(
                'label' => __('Resend Cooldown (seconds)', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::NUMBER,
                'min' => 30,
                'max' => 300,
                'step' => 10,
                'default' => 60,
                'condition' => array(
                    'enable_resend' => 'yes',
                ),
            )
        );
        
        $this->end_controls_section();
        
        // Style Section
        $this->start_controls_section(
            'style_section',
            array(
                'label' => __('Style', 'smart-auth'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            )
        );
        
        $this->add_control(
            'title_color',
            array(
                'label' => __('Title Color', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-otp-title' => 'color: {{VALUE}}',
                ),
            )
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            array(
                'name' => 'title_typography',
                'label' => __('Title Typography', 'smart-auth'),
                'selector' => '{{WRAPPER}} .smart-auth-otp-title',
            )
        );
        
        $this->add_control(
            'description_color',
            array(
                'label' => __('Description Color', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-otp-description' => 'color: {{VALUE}}',
                ),
            )
        );
        
        $this->add_control(
            'input_background',
            array(
                'label' => __('Input Background', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-input' => 'background-color: {{VALUE}}',
                ),
            )
        );
        
        $this->add_control(
            'input_border_color',
            array(
                'label' => __('Input Border Color', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-input' => 'border-color: {{VALUE}}',
                ),
            )
        );
        
        $this->add_control(
            'button_background',
            array(
                'label' => __('Button Background', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-button' => 'background-color: {{VALUE}}',
                ),
            )
        );
        
        $this->add_control(
            'button_text_color',
            array(
                'label' => __('Button Text Color', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-button' => 'color: {{VALUE}}',
                ),
            )
        );
        
        $this->add_control(
            'form_spacing',
            array(
                'label' => __('Form Element Spacing', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => array('px', 'em', 'rem'),
                'range' => array(
                    'px' => array(
                        'min' => 0,
                        'max' => 50,
                        'step' => 1,
                    ),
                ),
                'default' => array(
                    'unit' => 'px',
                    'size' => 16,
                ),
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-otp-form > *:not(:last-child)' => 'margin-bottom: {{SIZE}}{{UNIT}};',
                ),
            )
        );
        
        $this->end_controls_section();
    }
    
    /**
     * Render widget output on the frontend
     */
    protected function render() {
        $settings = $this->get_settings_for_display();
        
        $form_title = !empty($settings['form_title']) ? $settings['form_title'] : __('Phone Verification', 'smart-auth');
        $form_description = !empty($settings['form_description']) ? $settings['form_description'] : '';
        $phone_placeholder = !empty($settings['phone_placeholder']) ? $settings['phone_placeholder'] : __('Enter your phone number', 'smart-auth');
        $otp_placeholder = !empty($settings['otp_placeholder']) ? $settings['otp_placeholder'] : __('Enter verification code', 'smart-auth');
        $otp_provider = !empty($settings['otp_provider']) ? $settings['otp_provider'] : 'firebase';
        $default_country = !empty($settings['default_country']) ? $settings['default_country'] : 'US';
        $show_country_selector = $settings['show_country_selector'] === 'yes';
        $redirect_url = !empty($settings['redirect_url']['url']) ? $settings['redirect_url']['url'] : '';
        $enable_resend = $settings['enable_resend'] === 'yes';
        $resend_cooldown = !empty($settings['resend_cooldown']) ? (int) $settings['resend_cooldown'] : 60;
        
        // Build shortcode attributes
        $shortcode_atts = array(
            'title="' . esc_attr($form_title) . '"',
            'phone_placeholder="' . esc_attr($phone_placeholder) . '"',
            'otp_placeholder="' . esc_attr($otp_placeholder) . '"',
            'provider="' . esc_attr($otp_provider) . '"',
            'default_country="' . esc_attr($default_country) . '"',
            'show_country_selector="' . ($show_country_selector ? 'true' : 'false') . '"',
            'enable_resend="' . ($enable_resend ? 'true' : 'false') . '"',
            'resend_cooldown="' . esc_attr($resend_cooldown) . '"',
        );
        
        if (!empty($form_description)) {
            $shortcode_atts[] = 'description="' . esc_attr($form_description) . '"';
        }
        
        if (!empty($redirect_url)) {
            $shortcode_atts[] = 'redirect_url="' . esc_url($redirect_url) . '"';
        }
        
        // Render shortcode
        echo do_shortcode('[smart_auth_otp ' . implode(' ', $shortcode_atts) . ']');
    }
    
    /**
     * Render widget output in the editor
     */
    protected function content_template() {
        ?>
        <#
        var formTitle = settings.form_title || '<?php echo esc_js(__('Phone Verification', 'smart-auth')); ?>';
        var formDescription = settings.form_description || '';
        var phoneePlaceholder = settings.phone_placeholder || '<?php echo esc_js(__('Enter your phone number', 'smart-auth')); ?>';
        var otpPlaceholder = settings.otp_placeholder || '<?php echo esc_js(__('Enter verification code', 'smart-auth')); ?>';
        var showCountrySelector = settings.show_country_selector === 'yes';
        var enableResend = settings.enable_resend === 'yes';
        #>
        
        <div class="smart-auth-otp-widget">
            <h3 class="smart-auth-otp-title">{{{ formTitle }}}</h3>
            
            <# if (formDescription) { #>
                <p class="smart-auth-otp-description">{{{ formDescription }}}</p>
            <# } #>
            
            <div class="smart-auth-otp-form">
                <div class="phone-input-section">
                    <# if (showCountrySelector) { #>
                        <select class="smart-auth-country-selector">
                            <option value="US">🇺🇸 +1</option>
                            <option value="CA">🇨🇦 +1</option>
                            <option value="GB">🇬🇧 +44</option>
                        </select>
                    <# } #>
                    <input type="tel" class="smart-auth-input smart-auth-phone-input" placeholder="{{{ phoneePlaceholder }}}">
                </div>
                
                <button type="button" class="smart-auth-button smart-auth-send-otp">
                    <?php esc_html_e('Send Code', 'smart-auth'); ?>
                </button>
                
                <div class="otp-input-section" style="display: none;">
                    <input type="text" class="smart-auth-input smart-auth-otp-input" placeholder="{{{ otpPlaceholder }}}" maxlength="6">
                    <button type="button" class="smart-auth-button smart-auth-verify-otp">
                        <?php esc_html_e('Verify Code', 'smart-auth'); ?>
                    </button>
                    
                    <# if (enableResend) { #>
                        <button type="button" class="smart-auth-resend-link">
                            <?php esc_html_e('Resend Code', 'smart-auth'); ?>
                        </button>
                    <# } #>
                </div>
            </div>
            
            <p class="smart-auth-preview-note">
                <em><?php esc_html_e('This is a preview. The actual form will be functional on the frontend.', 'smart-auth'); ?></em>
            </p>
        </div>
        <?php
    }
}
