<?php
/**
 * Elementor Auth Form Widget
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Smart Auth Form Widget for Elementor
 */
class Smart_Auth_Elementor_Auth_Form_Widget extends \Elementor\Widget_Base {
    
    /**
     * Get widget name
     *
     * @return string Widget name
     */
    public function get_name() {
        return 'smart-auth-form';
    }
    
    /**
     * Get widget title
     *
     * @return string Widget title
     */
    public function get_title() {
        return __('Smart Auth Form', 'smart-auth');
    }
    
    /**
     * Get widget icon
     *
     * @return string Widget icon
     */
    public function get_icon() {
        return 'eicon-form-horizontal';
    }
    
    /**
     * Get widget categories
     *
     * @return array Widget categories
     */
    public function get_categories() {
        return array('smart-auth');
    }
    
    /**
     * Get widget keywords
     *
     * @return array Widget keywords
     */
    public function get_keywords() {
        return array('auth', 'login', 'register', 'firebase', 'authentication');
    }
    
    /**
     * Register widget controls
     */
    protected function register_controls() {
        // Content Section
        $this->start_controls_section(
            'content_section',
            array(
                'label' => __('Content', 'smart-auth'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            )
        );
        
        $this->add_control(
            'form_title',
            array(
                'label' => __('Form Title', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Sign In', 'smart-auth'),
                'placeholder' => __('Enter form title', 'smart-auth'),
            )
        );
        
        $this->add_control(
            'show_providers',
            array(
                'label' => __('Show Providers', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SELECT2,
                'multiple' => true,
                'options' => array(
                    'google' => __('Google', 'smart-auth'),
                    'facebook' => __('Facebook', 'smart-auth'),
                    'apple' => __('Apple', 'smart-auth'),
                    'phone' => __('Phone Number', 'smart-auth'),
                ),
                'default' => array('google', 'facebook', 'phone'),
            )
        );
        
        $this->add_control(
            'redirect_url',
            array(
                'label' => __('Redirect URL', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::URL,
                'placeholder' => __('https://your-domain.com', 'smart-auth'),
                'show_external' => true,
                'default' => array(
                    'url' => '',
                    'is_external' => false,
                    'nofollow' => false,
                ),
            )
        );
        
        $this->add_control(
            'form_style',
            array(
                'label' => __('Form Style', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'options' => array(
                    'default' => __('Default', 'smart-auth'),
                    'minimal' => __('Minimal', 'smart-auth'),
                    'modern' => __('Modern', 'smart-auth'),
                ),
                'default' => 'default',
            )
        );
        
        $this->end_controls_section();
        
        // Style Section
        $this->start_controls_section(
            'style_section',
            array(
                'label' => __('Style', 'smart-auth'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            )
        );
        
        $this->add_control(
            'title_color',
            array(
                'label' => __('Title Color', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-form-title' => 'color: {{VALUE}}',
                ),
            )
        );
        
        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            array(
                'name' => 'title_typography',
                'label' => __('Title Typography', 'smart-auth'),
                'selector' => '{{WRAPPER}} .smart-auth-form-title',
            )
        );
        
        $this->add_control(
            'button_color',
            array(
                'label' => __('Button Color', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-button' => 'background-color: {{VALUE}}',
                ),
            )
        );
        
        $this->add_control(
            'button_text_color',
            array(
                'label' => __('Button Text Color', 'smart-auth'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => array(
                    '{{WRAPPER}} .smart-auth-button' => 'color: {{VALUE}}',
                ),
            )
        );
        
        $this->end_controls_section();
    }
    
    /**
     * Render widget output on the frontend
     */
    protected function render() {
        $settings = $this->get_settings_for_display();
        
        $form_title = !empty($settings['form_title']) ? $settings['form_title'] : __('Sign In', 'smart-auth');
        $show_providers = !empty($settings['show_providers']) ? $settings['show_providers'] : array('google', 'facebook', 'phone');
        $redirect_url = !empty($settings['redirect_url']['url']) ? $settings['redirect_url']['url'] : '';
        $form_style = !empty($settings['form_style']) ? $settings['form_style'] : 'default';
        
        // Build shortcode attributes
        $shortcode_atts = array(
            'title="' . esc_attr($form_title) . '"',
            'show_providers="' . esc_attr(implode(',', $show_providers)) . '"',
            'form_style="' . esc_attr($form_style) . '"',
        );
        
        if (!empty($redirect_url)) {
            $shortcode_atts[] = 'redirect_url="' . esc_url($redirect_url) . '"';
        }
        
        // Render shortcode
        echo do_shortcode('[smart_auth_form ' . implode(' ', $shortcode_atts) . ']');
    }
    
    /**
     * Render widget output in the editor
     */
    protected function content_template() {
        ?>
        <#
        var formTitle = settings.form_title || '<?php echo esc_js(__('Sign In', 'smart-auth')); ?>';
        var showProviders = settings.show_providers || ['google', 'facebook', 'phone'];
        var formStyle = settings.form_style || 'default';
        #>
        <div class="smart-auth-form smart-auth-form-{{ formStyle }}">
            <h3 class="smart-auth-form-title">{{{ formTitle }}}</h3>
            <div class="smart-auth-providers">
                <# _.each(showProviders, function(provider) { #>
                    <button class="smart-auth-button smart-auth-{{ provider }}-button">
                        <?php esc_html_e('Sign in with', 'smart-auth'); ?> {{ provider.charAt(0).toUpperCase() + provider.slice(1) }}
                    </button>
                <# }); #>
            </div>
            <p class="smart-auth-preview-note">
                <em><?php esc_html_e('This is a preview. The actual form will be functional on the frontend.', 'smart-auth'); ?></em>
            </p>
        </div>
        <?php
    }
}
