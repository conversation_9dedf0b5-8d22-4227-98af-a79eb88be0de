/**
 * Smart Auth Frontend Styles
 *
 * @package SmartAuth
 * @since 1.0.0
 */

/* ==========================================================================
   Base Styles
   ========================================================================== */

.smart-auth-form {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.smart-auth-form-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.smart-auth-form-description {
    text-align: center;
    margin-bottom: 20px;
    color: #666;
    font-size: 14px;
}

/* ==========================================================================
   Provider Buttons
   ========================================================================== */

.smart-auth-providers {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.smart-auth-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: #fff;
    color: #333;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.smart-auth-button:hover {
    background: #f8f9fa;
    border-color: #ccc;
    transform: translateY(-1px);
}

.smart-auth-button:active {
    transform: translateY(0);
}

.smart-auth-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.smart-auth-button.loading {
    color: transparent;
}

.smart-auth-button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #ddd;
    border-top-color: #333;
    border-radius: 50%;
    animation: smart-auth-spin 1s linear infinite;
}

/* Provider-specific button styles */
.smart-auth-google-button {
    background: #fff;
    border-color: #dadce0;
    color: #3c4043;
}

.smart-auth-google-button:hover {
    background: #f8f9fa;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.smart-auth-facebook-button {
    background: #1877f2;
    border-color: #1877f2;
    color: #fff;
}

.smart-auth-facebook-button:hover {
    background: #166fe5;
    border-color: #166fe5;
}

.smart-auth-apple-button {
    background: #000;
    border-color: #000;
    color: #fff;
}

.smart-auth-apple-button:hover {
    background: #333;
    border-color: #333;
}

.smart-auth-phone-button {
    background: #28a745;
    border-color: #28a745;
    color: #fff;
}

.smart-auth-phone-button:hover {
    background: #218838;
    border-color: #218838;
}

/* ==========================================================================
   Phone Input
   ========================================================================== */

.smart-auth-phone-section {
    margin-bottom: 20px;
}

.smart-auth-phone-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    margin-bottom: 12px;
}

.smart-auth-phone-input:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
}

/* ==========================================================================
   OTP Form
   ========================================================================== */

.smart-auth-otp-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.smart-auth-otp-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.smart-auth-otp-code {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    text-align: center;
    letter-spacing: 2px;
}

.smart-auth-otp-code:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
}

.smart-auth-verify-otp {
    background: #007cba;
    border-color: #007cba;
    color: #fff;
}

.smart-auth-verify-otp:hover {
    background: #005a87;
    border-color: #005a87;
}

.smart-auth-resend-otp {
    background: transparent;
    border: none;
    color: #007cba;
    text-decoration: underline;
    cursor: pointer;
    font-size: 14px;
}

.smart-auth-resend-otp:hover {
    color: #005a87;
}

/* ==========================================================================
   Messages
   ========================================================================== */

.smart-auth-message {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
}

.smart-auth-message.smart-auth-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.smart-auth-message.smart-auth-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.smart-auth-message.smart-auth-info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

/* ==========================================================================
   Form Styles
   ========================================================================== */

/* Minimal Style */
.smart-auth-form.smart-auth-form-minimal {
    background: transparent;
    box-shadow: none;
    border: 1px solid #eee;
}

.smart-auth-form.smart-auth-form-minimal .smart-auth-button {
    border-radius: 4px;
    font-weight: 400;
}

/* Modern Style */
.smart-auth-form.smart-auth-form-modern {
    border-radius: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
}

.smart-auth-form.smart-auth-form-modern .smart-auth-form-title {
    color: #fff;
}

.smart-auth-form.smart-auth-form-modern .smart-auth-form-description {
    color: rgba(255, 255, 255, 0.8);
}

.smart-auth-form.smart-auth-form-modern .smart-auth-button {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    color: #fff;
    backdrop-filter: blur(10px);
}

.smart-auth-form.smart-auth-form-modern .smart-auth-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 480px) {
    .smart-auth-form {
        margin: 0 16px;
        padding: 16px;
    }
    
    .smart-auth-form-title {
        font-size: 20px;
    }
    
    .smart-auth-button {
        padding: 14px 16px;
        font-size: 16px;
    }
}

/* ==========================================================================
   Animations
   ========================================================================== */

@keyframes smart-auth-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.smart-auth-form {
    animation: smart-auth-fade-in 0.3s ease-out;
}

@keyframes smart-auth-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ==========================================================================
   RTL Support
   ========================================================================== */

[dir="rtl"] .smart-auth-button {
    text-align: center;
}

[dir="rtl"] .smart-auth-otp-code {
    direction: ltr;
}

/* ==========================================================================
   High Contrast Mode
   ========================================================================== */

@media (prefers-contrast: high) {
    .smart-auth-button {
        border-width: 2px;
    }
    
    .smart-auth-button:focus {
        outline: 2px solid;
        outline-offset: 2px;
    }
}

/* ==========================================================================
   Reduced Motion
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
    .smart-auth-button,
    .smart-auth-form {
        animation: none;
        transition: none;
    }
    
    .smart-auth-button:hover {
        transform: none;
    }
}
