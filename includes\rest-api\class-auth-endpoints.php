<?php
/**
 * Authentication REST API Endpoints
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Authentication REST API endpoints class
 */
class Smart_Auth_Auth_Endpoints {
    
    /**
     * API namespace
     *
     * @var string
     */
    private $namespace = 'smart-auth/v1';
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('rest_api_init', array($this, 'register_routes'));
    }
    
    /**
     * Register REST API routes
     */
    public function register_routes() {
        // Firebase ID token verification
        register_rest_route($this->namespace, '/firebase-verify', array(
            'methods' => 'POST',
            'callback' => array($this, 'firebase_verify'),
            'permission_callback' => '__return_true',
            'args' => array(
                'id_token' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
            ),
        ));
        
        // Send OTP to phone number
        register_rest_route($this->namespace, '/phone-send-otp', array(
            'methods' => 'POST',
            'callback' => array($this, 'send_phone_otp'),
            'permission_callback' => '__return_true',
            'args' => array(
                'phone_number' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'provider' => array(
                    'required' => false,
                    'type' => 'string',
                    'default' => 'firebase',
                    'enum' => array('firebase', 'twilio'),
                ),
            ),
        ));
        
        // Verify OTP code
        register_rest_route($this->namespace, '/phone-verify-otp', array(
            'methods' => 'POST',
            'callback' => array($this, 'verify_phone_otp'),
            'permission_callback' => '__return_true',
            'args' => array(
                'phone_number' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'otp_code' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'session_info' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'provider' => array(
                    'required' => false,
                    'type' => 'string',
                    'default' => 'firebase',
                    'enum' => array('firebase', 'twilio'),
                ),
            ),
        ));
        
        // Create WordPress user from Firebase data
        register_rest_route($this->namespace, '/create-user', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_user'),
            'permission_callback' => '__return_true',
            'args' => array(
                'firebase_user' => array(
                    'required' => true,
                    'type' => 'object',
                ),
                'auth_provider' => array(
                    'required' => true,
                    'type' => 'string',
                    'enum' => array('google', 'facebook', 'apple', 'phone'),
                ),
            ),
        ));
        
        // Refresh JWT token
        register_rest_route($this->namespace, '/refresh-token', array(
            'methods' => 'POST',
            'callback' => array($this, 'refresh_token'),
            'permission_callback' => array($this, 'check_jwt_auth'),
            'args' => array(
                'token' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
            ),
        ));
    }
    
    /**
     * Verify Firebase ID token
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function firebase_verify($request) {
        // Get parameters
        $id_token = $request->get_param('id_token');
        $provider = $request->get_param('provider');

        if (empty($id_token)) {
            return new WP_Error('missing_token', __('ID token is required.', 'smart-auth'), array('status' => 400));
        }

        // Rate limiting check
        $rate_limit_check = $this->check_rate_limit();
        if (is_wp_error($rate_limit_check)) {
            return $rate_limit_check;
        }

        // Get Firebase auth handler
        $firebase_auth = new Smart_Auth_Firebase();

        // Verify the token
        $firebase_user = $firebase_auth->verify_id_token($id_token);
        if (is_wp_error($firebase_user)) {
            $this->log_failed_attempt();
            return $firebase_user;
        }

        // Get user sync handler
        $user_sync = new Smart_Auth_User_Sync();

        // Sync or create WordPress user
        $wp_user = $user_sync->sync_user($firebase_user, $provider ?: 'firebase');
        if (is_wp_error($wp_user)) {
            return $wp_user;
        }

        // Generate JWT token
        $jwt_handler = new Smart_Auth_JWT_Handler();
        $jwt_token = $jwt_handler->generate_token($wp_user, array(
            'firebase_uid' => $firebase_user['firebase_uid'],
            'auth_provider' => $provider ?: 'firebase',
        ));

        if (is_wp_error($jwt_token)) {
            return $jwt_token;
        }

        // Log the user in
        wp_set_current_user($wp_user->ID);
        wp_set_auth_cookie($wp_user->ID);

        // Fire action
        do_action('smart_auth_user_authenticated', $wp_user, $firebase_user, 'firebase');

        return new WP_REST_Response(array(
            'success' => true,
            'message' => __('Authentication successful.', 'smart-auth'),
            'data' => array(
                'user_id' => $wp_user->ID,
                'user_login' => $wp_user->user_login,
                'user_email' => $wp_user->user_email,
                'display_name' => $wp_user->display_name,
                'jwt_token' => $jwt_token,
                'firebase_uid' => $firebase_user['firebase_uid'],
                'auth_provider' => $provider ?: 'firebase',
            ),
        ), 200);
    }
    
    /**
     * Send OTP to phone number
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function send_phone_otp($request) {
        // Get parameters
        $phone_number = $request->get_param('phone_number');
        $provider = $request->get_param('provider');

        if (empty($phone_number)) {
            return new WP_Error('missing_phone', __('Phone number is required.', 'smart-auth'), array('status' => 400));
        }

        // Rate limiting check
        $rate_limit_check = $this->check_rate_limit($phone_number);
        if (is_wp_error($rate_limit_check)) {
            return $rate_limit_check;
        }

        // Security checks
        $security_check = $this->check_security_restrictions($phone_number);
        if (is_wp_error($security_check)) {
            return $security_check;
        }

        // Choose provider
        $provider = $provider ?: 'firebase';

        if ($provider === 'twilio') {
            $auth_handler = new Smart_Auth_Twilio();
        } else {
            $auth_handler = new Smart_Auth_Firebase();
        }

        // Send OTP
        $result = $auth_handler->send_otp($phone_number);
        if (is_wp_error($result)) {
            $this->log_failed_attempt($phone_number);
            return $result;
        }

        // Update rate limiting
        $this->update_rate_limit($phone_number);

        return new WP_REST_Response(array(
            'success' => true,
            'message' => __('OTP sent successfully.', 'smart-auth'),
            'data' => array(
                'phone_number' => $phone_number,
                'provider' => $provider,
                'session_info' => isset($result['session_info']) ? $result['session_info'] : null,
                'sid' => isset($result['sid']) ? $result['sid'] : null,
            ),
        ), 200);
    }
    
    /**
     * Verify phone OTP
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function verify_phone_otp($request) {
        // Get parameters
        $phone_number = $request->get_param('phone_number');
        $otp_code = $request->get_param('otp_code');
        $session_info = $request->get_param('session_info');
        $provider = $request->get_param('provider');

        if (empty($phone_number) || empty($otp_code)) {
            return new WP_Error('missing_params', __('Phone number and OTP code are required.', 'smart-auth'), array('status' => 400));
        }

        // Rate limiting check
        $rate_limit_check = $this->check_rate_limit($phone_number);
        if (is_wp_error($rate_limit_check)) {
            return $rate_limit_check;
        }

        // Choose provider
        $provider = $provider ?: 'firebase';

        if ($provider === 'twilio') {
            $auth_handler = new Smart_Auth_Twilio();
            $result = $auth_handler->verify_otp($phone_number, $otp_code);
        } else {
            $auth_handler = new Smart_Auth_Firebase();
            $result = $auth_handler->verify_phone_otp($session_info, $otp_code);
        }

        if (is_wp_error($result)) {
            $this->log_failed_attempt($phone_number);
            return $result;
        }

        // For Twilio, we need to create user data
        if ($provider === 'twilio') {
            $firebase_user = array(
                'firebase_uid' => 'phone_' . md5($phone_number),
                'phone_number' => $phone_number,
                'email' => '',
                'email_verified' => false,
                'name' => '',
                'picture' => '',
                'provider_id' => 'phone',
            );
        } else {
            $firebase_user = $result;
        }

        // Get user sync handler
        $user_sync = new Smart_Auth_User_Sync();

        // Sync or create WordPress user
        $wp_user = $user_sync->sync_user($firebase_user, 'phone');
        if (is_wp_error($wp_user)) {
            return $wp_user;
        }

        // Generate JWT token
        $jwt_handler = new Smart_Auth_JWT_Handler();
        $jwt_token = $jwt_handler->generate_token($wp_user, array(
            'phone_number' => $phone_number,
            'auth_provider' => 'phone',
        ));

        if (is_wp_error($jwt_token)) {
            return $jwt_token;
        }

        // Log the user in
        wp_set_current_user($wp_user->ID);
        wp_set_auth_cookie($wp_user->ID);

        // Fire action
        do_action('smart_auth_user_authenticated', $wp_user, $firebase_user, 'phone');

        return new WP_REST_Response(array(
            'success' => true,
            'message' => __('Phone verification successful.', 'smart-auth'),
            'data' => array(
                'user_id' => $wp_user->ID,
                'user_login' => $wp_user->user_login,
                'user_email' => $wp_user->user_email,
                'display_name' => $wp_user->display_name,
                'jwt_token' => $jwt_token,
                'phone_number' => $phone_number,
                'auth_provider' => 'phone',
            ),
        ), 200);
    }
    
    /**
     * Create WordPress user
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function create_user($request) {
        // Get parameters
        $firebase_user = $request->get_param('firebase_user');
        $auth_provider = $request->get_param('auth_provider');

        if (empty($firebase_user) || empty($auth_provider)) {
            return new WP_Error('missing_params', __('Firebase user data and auth provider are required.', 'smart-auth'), array('status' => 400));
        }

        // Validate auth provider
        $allowed_providers = array('google', 'facebook', 'apple', 'phone');
        if (!in_array($auth_provider, $allowed_providers)) {
            return new WP_Error('invalid_provider', __('Invalid authentication provider.', 'smart-auth'), array('status' => 400));
        }

        // Get user sync handler
        $user_sync = new Smart_Auth_User_Sync();

        // Sync or create WordPress user
        $wp_user = $user_sync->sync_user($firebase_user, $auth_provider);
        if (is_wp_error($wp_user)) {
            return $wp_user;
        }

        // Generate JWT token
        $jwt_handler = new Smart_Auth_JWT_Handler();
        $jwt_token = $jwt_handler->generate_token($wp_user, array(
            'firebase_uid' => $firebase_user['firebase_uid'],
            'auth_provider' => $auth_provider,
        ));

        if (is_wp_error($jwt_token)) {
            return $jwt_token;
        }

        return new WP_REST_Response(array(
            'success' => true,
            'message' => __('User created successfully.', 'smart-auth'),
            'data' => array(
                'user_id' => $wp_user->ID,
                'user_login' => $wp_user->user_login,
                'user_email' => $wp_user->user_email,
                'display_name' => $wp_user->display_name,
                'jwt_token' => $jwt_token,
                'firebase_uid' => $firebase_user['firebase_uid'],
                'auth_provider' => $auth_provider,
            ),
        ), 201);
    }

    /**
     * Refresh JWT token
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function refresh_token($request) {
        // Get parameters
        $token = $request->get_param('token');

        if (empty($token)) {
            return new WP_Error('missing_token', __('JWT token is required.', 'smart-auth'), array('status' => 400));
        }

        // Get JWT handler
        $jwt_handler = new Smart_Auth_JWT_Handler();

        // Refresh token
        $new_token = $jwt_handler->refresh_token($token);
        if (is_wp_error($new_token)) {
            return $new_token;
        }

        return new WP_REST_Response(array(
            'success' => true,
            'message' => __('Token refreshed successfully.', 'smart-auth'),
            'data' => array(
                'jwt_token' => $new_token,
            ),
        ), 200);
    }

    /**
     * Check JWT authentication
     *
     * @param WP_REST_Request $request Request object
     * @return bool|WP_Error True if authenticated, error otherwise
     */
    public function check_jwt_auth($request) {
        // Get JWT handler
        $jwt_handler = new Smart_Auth_JWT_Handler();

        // Get token from header
        $token = $jwt_handler->get_token_from_header();

        if (empty($token)) {
            return new WP_Error('missing_token', __('JWT token is required.', 'smart-auth'), array('status' => 401));
        }

        // Validate token
        $payload = $jwt_handler->validate_token($token);
        if (is_wp_error($payload)) {
            return $payload;
        }

        // Set current user
        if (isset($payload['data']['user']['id'])) {
            wp_set_current_user($payload['data']['user']['id']);
        }

        return true;
    }

    /**
     * Check rate limiting
     *
     * @param string $identifier Identifier for rate limiting (IP or phone)
     * @return bool|WP_Error True if allowed, error if rate limited
     */
    private function check_rate_limit($identifier = null) {
        $security_settings = get_option('smart_auth_security_settings', array());
        $max_attempts = isset($security_settings['rate_limit_attempts']) ? (int) $security_settings['rate_limit_attempts'] : 3;
        $time_window = isset($security_settings['rate_limit_window']) ? (int) $security_settings['rate_limit_window'] : 15 * MINUTE_IN_SECONDS;

        // Use IP address if no identifier provided
        if (empty($identifier)) {
            $identifier = $this->get_client_ip();
        }

        $cache_key = 'smart_auth_rate_limit_' . md5($identifier);
        $attempts = get_transient($cache_key);

        if ($attempts === false) {
            $attempts = 0;
        }

        if ($attempts >= $max_attempts) {
            return new WP_Error('rate_limited', sprintf(
                __('Too many requests. Please wait %d minutes before trying again.', 'smart-auth'),
                ceil($time_window / 60)
            ), array('status' => 429));
        }

        return true;
    }

    /**
     * Update rate limiting counter
     *
     * @param string $identifier Identifier for rate limiting
     */
    private function update_rate_limit($identifier = null) {
        $security_settings = get_option('smart_auth_security_settings', array());
        $time_window = isset($security_settings['rate_limit_window']) ? (int) $security_settings['rate_limit_window'] : 15 * MINUTE_IN_SECONDS;

        // Use IP address if no identifier provided
        if (empty($identifier)) {
            $identifier = $this->get_client_ip();
        }

        $cache_key = 'smart_auth_rate_limit_' . md5($identifier);
        $attempts = get_transient($cache_key);

        if ($attempts === false) {
            $attempts = 0;
        }

        set_transient($cache_key, $attempts + 1, $time_window);
    }

    /**
     * Log failed authentication attempt
     *
     * @param string $identifier Identifier (phone number or IP)
     */
    private function log_failed_attempt($identifier = null) {
        $security_settings = get_option('smart_auth_security_settings', array());

        if (!isset($security_settings['enable_failed_login_protection']) || !$security_settings['enable_failed_login_protection']) {
            return;
        }

        // Use IP address if no identifier provided
        if (empty($identifier)) {
            $identifier = $this->get_client_ip();
        }

        $cache_key = 'smart_auth_failed_attempts_' . md5($identifier);
        $attempts = get_transient($cache_key);

        if ($attempts === false) {
            $attempts = 0;
        }

        $max_attempts = isset($security_settings['max_failed_attempts']) ? (int) $security_settings['max_failed_attempts'] : 5;
        $lockout_duration = isset($security_settings['lockout_duration']) ? (int) $security_settings['lockout_duration'] : 30 * MINUTE_IN_SECONDS;

        $attempts++;
        set_transient($cache_key, $attempts, $lockout_duration);

        // Log to WordPress if too many attempts
        if ($attempts >= $max_attempts) {
            error_log("Smart Auth: Too many failed attempts from {$identifier}");
        }
    }

    /**
     * Check security restrictions
     *
     * @param string $phone_number Phone number to check
     * @return bool|WP_Error True if allowed, error if blocked
     */
    private function check_security_restrictions($phone_number = null) {
        $security_settings = get_option('smart_auth_security_settings', array());

        // Check HTTPS requirement
        if (isset($security_settings['enable_https_only']) && $security_settings['enable_https_only'] && !is_ssl()) {
            return new WP_Error('https_required', __('HTTPS is required for authentication.', 'smart-auth'), array('status' => 400));
        }

        // Check IP blocking
        if (isset($security_settings['enable_ip_blocking']) && $security_settings['enable_ip_blocking']) {
            $blocked_ips = isset($security_settings['blocked_ips']) ? $security_settings['blocked_ips'] : '';
            $blocked_ips_array = array_filter(array_map('trim', explode("\n", $blocked_ips)));
            $client_ip = $this->get_client_ip();

            if (in_array($client_ip, $blocked_ips_array)) {
                return new WP_Error('ip_blocked', __('Your IP address is blocked.', 'smart-auth'), array('status' => 403));
            }
        }

        // Check failed login protection
        if (isset($security_settings['enable_failed_login_protection']) && $security_settings['enable_failed_login_protection']) {
            $identifier = $phone_number ?: $this->get_client_ip();
            $cache_key = 'smart_auth_failed_attempts_' . md5($identifier);
            $attempts = get_transient($cache_key);
            $max_attempts = isset($security_settings['max_failed_attempts']) ? (int) $security_settings['max_failed_attempts'] : 5;

            if ($attempts !== false && $attempts >= $max_attempts) {
                return new WP_Error('account_locked', __('Account temporarily locked due to too many failed attempts.', 'smart-auth'), array('status' => 423));
            }
        }

        return true;
    }

    /**
     * Get client IP address
     *
     * @return string Client IP address
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR');

        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);

                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }

        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }
}
