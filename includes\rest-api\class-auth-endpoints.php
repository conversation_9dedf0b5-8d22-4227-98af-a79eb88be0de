<?php
/**
 * Authentication REST API Endpoints
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Authentication REST API endpoints class
 */
class Smart_Auth_Auth_Endpoints {
    
    /**
     * API namespace
     *
     * @var string
     */
    private $namespace = 'smart-auth/v1';
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('rest_api_init', array($this, 'register_routes'));
    }
    
    /**
     * Register REST API routes
     */
    public function register_routes() {
        // Firebase ID token verification
        register_rest_route($this->namespace, '/firebase-verify', array(
            'methods' => 'POST',
            'callback' => array($this, 'firebase_verify'),
            'permission_callback' => '__return_true',
            'args' => array(
                'id_token' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
            ),
        ));
        
        // Send OTP to phone number
        register_rest_route($this->namespace, '/phone-send-otp', array(
            'methods' => 'POST',
            'callback' => array($this, 'send_phone_otp'),
            'permission_callback' => '__return_true',
            'args' => array(
                'phone_number' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'provider' => array(
                    'required' => false,
                    'type' => 'string',
                    'default' => 'firebase',
                    'enum' => array('firebase', 'twilio'),
                ),
            ),
        ));
        
        // Verify OTP code
        register_rest_route($this->namespace, '/phone-verify-otp', array(
            'methods' => 'POST',
            'callback' => array($this, 'verify_phone_otp'),
            'permission_callback' => '__return_true',
            'args' => array(
                'phone_number' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'otp_code' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'session_info' => array(
                    'required' => false,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
                'provider' => array(
                    'required' => false,
                    'type' => 'string',
                    'default' => 'firebase',
                    'enum' => array('firebase', 'twilio'),
                ),
            ),
        ));
        
        // Create WordPress user from Firebase data
        register_rest_route($this->namespace, '/create-user', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_user'),
            'permission_callback' => '__return_true',
            'args' => array(
                'firebase_user' => array(
                    'required' => true,
                    'type' => 'object',
                ),
                'auth_provider' => array(
                    'required' => true,
                    'type' => 'string',
                    'enum' => array('google', 'facebook', 'apple', 'phone'),
                ),
            ),
        ));
        
        // Refresh JWT token
        register_rest_route($this->namespace, '/refresh-token', array(
            'methods' => 'POST',
            'callback' => array($this, 'refresh_token'),
            'permission_callback' => array($this, 'check_jwt_auth'),
            'args' => array(
                'token' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                ),
            ),
        ));
    }
    
    /**
     * Verify Firebase ID token
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function firebase_verify($request) {
        // Implementation will be added in the next task
        return new WP_Error('not_implemented', 'Firebase verification endpoint not implemented yet', array('status' => 501));
    }
    
    /**
     * Send OTP to phone number
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function send_phone_otp($request) {
        // Implementation will be added in the next task
        return new WP_Error('not_implemented', 'Send OTP endpoint not implemented yet', array('status' => 501));
    }
    
    /**
     * Verify phone OTP
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function verify_phone_otp($request) {
        // Implementation will be added in the next task
        return new WP_Error('not_implemented', 'Verify OTP endpoint not implemented yet', array('status' => 501));
    }
    
    /**
     * Create WordPress user
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function create_user($request) {
        // Implementation will be added in the next task
        return new WP_Error('not_implemented', 'Create user endpoint not implemented yet', array('status' => 501));
    }
    
    /**
     * Refresh JWT token
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object or error
     */
    public function refresh_token($request) {
        // Implementation will be added in the next task
        return new WP_Error('not_implemented', 'Refresh token endpoint not implemented yet', array('status' => 501));
    }
    
    /**
     * Check JWT authentication
     *
     * @param WP_REST_Request $request Request object
     * @return bool|WP_Error True if authenticated, error otherwise
     */
    public function check_jwt_auth($request) {
        // Implementation will be added in the next task
        return new WP_Error('not_implemented', 'JWT auth check not implemented yet', array('status' => 501));
    }
}
