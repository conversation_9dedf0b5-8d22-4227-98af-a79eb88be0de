/**
 * Smart Auth OTP Handler
 *
 * @package SmartAuth
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    /**
     * OTP Handler object
     */
    window.SmartAuthOTP = {
        
        /**
         * Current form element
         */
        currentForm: null,
        
        /**
         * Current phone number
         */
        currentPhone: null,
        
        /**
         * Session info for Firebase
         */
        sessionInfo: null,
        
        /**
         * Confirmation result for Firebase
         */
        confirmationResult: null,
        
        /**
         * Resend timer
         */
        resendTimer: null,
        
        /**
         * Initialize OTP handler
         */
        init: function() {
            this.bindEvents();
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            var self = this;
            
            // Send OTP button
            $(document).on('click', '.smart-auth-send-otp', function(e) {
                e.preventDefault();
                self.sendOTP($(this));
            });
            
            // Verify OTP button
            $(document).on('click', '.smart-auth-verify-otp', function(e) {
                e.preventDefault();
                self.verifyOTP($(this));
            });
            
            // Resend OTP button
            $(document).on('click', '.smart-auth-resend-button:not(:disabled)', function(e) {
                e.preventDefault();
                self.resendOTP($(this));
            });
            
            // Auto-submit on OTP input complete
            $(document).on('input', '.smart-auth-otp-input', function() {
                self.handleOTPInput($(this));
            });
            
            // Phone number input formatting
            $(document).on('input', '.smart-auth-phone-input', function() {
                self.formatPhoneInput($(this));
            });
            
            // Country selector change
            $(document).on('change', '.smart-auth-country-selector', function() {
                self.updateCountryCode($(this));
            });
        },
        
        /**
         * Send OTP
         */
        sendOTP: function($button) {
            var self = this;
            var $form = $button.closest('.smart-auth-otp-form');
            var $phoneInput = $form.find('.smart-auth-phone-input');
            var phoneNumber = $phoneInput.val().trim();
            var provider = $form.data('provider') || 'firebase';
            
            if (!phoneNumber) {
                this.showError($form, 'Please enter your phone number.');
                return;
            }
            
            // Validate phone number if enabled
            if ($form.data('phone-validation') && !this.isValidPhoneNumber(phoneNumber)) {
                this.showError($form, 'Please enter a valid phone number.');
                return;
            }
            
            // Format phone number
            var formattedPhone = this.formatPhoneNumber($form, phoneNumber);
            $form.find('.smart-auth-formatted-phone').val(formattedPhone);
            
            this.currentForm = $form;
            this.currentPhone = formattedPhone;
            
            // Show loading state
            this.setButtonLoading($button, 'Sending...');
            this.clearMessages($form);
            
            if (provider === 'firebase' && window.SmartAuthFirebase) {
                this.sendFirebaseOTP($form, formattedPhone);
            } else {
                this.sendTwilioOTP($form, formattedPhone);
            }
        },
        
        /**
         * Send OTP via Firebase
         */
        sendFirebaseOTP: function($form, phoneNumber) {
            var self = this;
            
            // Initialize reCAPTCHA if not already done
            if (!window.recaptchaVerifier) {
                window.recaptchaVerifier = new firebase.auth.RecaptchaVerifier('recaptcha-container', {
                    'size': 'invisible',
                    'callback': function(response) {
                        // reCAPTCHA solved
                    }
                });
            }
            
            SmartAuthFirebase.sendPhoneVerification(phoneNumber, window.recaptchaVerifier)
                .then(function(result) {
                    if (result.success) {
                        self.confirmationResult = result.confirmationResult;
                        self.sessionInfo = result.sessionInfo;
                        $form.find('.smart-auth-session-info').val(self.sessionInfo);
                        
                        self.showOTPSection($form);
                        self.showSuccess($form, 'Verification code sent successfully!');
                    } else {
                        self.showError($form, result.message);
                    }
                })
                .catch(function(error) {
                    self.showError($form, error.message || 'Failed to send verification code.');
                })
                .finally(function() {
                    self.resetButtonLoading($form.find('.smart-auth-send-otp'), 'Send Code');
                });
        },
        
        /**
         * Send OTP via Twilio (AJAX)
         */
        sendTwilioOTP: function($form, phoneNumber) {
            var self = this;
            
            $.ajax({
                url: smartAuthAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'smart_auth_send_otp',
                    nonce: smartAuthAjax.nonce,
                    phone_number: phoneNumber,
                    provider: 'twilio'
                },
                success: function(response) {
                    if (response.success) {
                        self.showOTPSection($form);
                        self.showSuccess($form, response.data.message);
                    } else {
                        self.showError($form, response.data.message);
                    }
                },
                error: function() {
                    self.showError($form, 'Failed to send verification code. Please try again.');
                },
                complete: function() {
                    self.resetButtonLoading($form.find('.smart-auth-send-otp'), 'Send Code');
                }
            });
        },
        
        /**
         * Verify OTP
         */
        verifyOTP: function($button) {
            var self = this;
            var $form = $button.closest('.smart-auth-otp-form');
            var $otpInput = $form.find('.smart-auth-otp-input');
            var otpCode = $otpInput.val().trim();
            var provider = $form.data('provider') || 'firebase';
            
            if (!otpCode) {
                this.showError($form, 'Please enter the verification code.');
                return;
            }
            
            var expectedLength = $form.data('otp-length') || 6;
            if (otpCode.length !== expectedLength) {
                this.showError($form, 'Please enter a valid ' + expectedLength + '-digit code.');
                return;
            }
            
            // Show loading state
            this.setButtonLoading($button, 'Verifying...');
            this.clearMessages($form);
            
            if (provider === 'firebase' && this.confirmationResult) {
                this.verifyFirebaseOTP($form, otpCode);
            } else {
                this.verifyTwilioOTP($form, otpCode);
            }
        },
        
        /**
         * Verify OTP via Firebase
         */
        verifyFirebaseOTP: function($form, otpCode) {
            var self = this;
            
            SmartAuthFirebase.verifyPhoneOTP(this.confirmationResult, otpCode)
                .then(function(result) {
                    if (result.success) {
                        self.handleVerificationSuccess($form, result);
                    } else {
                        self.showError($form, result.message);
                    }
                })
                .catch(function(error) {
                    self.showError($form, error.message || 'Invalid verification code.');
                })
                .finally(function() {
                    self.resetButtonLoading($form.find('.smart-auth-verify-otp'), 'Verify Code');
                });
        },
        
        /**
         * Verify OTP via Twilio (AJAX)
         */
        verifyTwilioOTP: function($form, otpCode) {
            var self = this;
            
            $.ajax({
                url: smartAuthAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'smart_auth_verify_otp',
                    nonce: smartAuthAjax.nonce,
                    phone_number: this.currentPhone,
                    otp_code: otpCode,
                    provider: 'twilio'
                },
                success: function(response) {
                    if (response.success) {
                        self.handleVerificationSuccess($form, response.data);
                    } else {
                        self.showError($form, response.data.message);
                    }
                },
                error: function() {
                    self.showError($form, 'Verification failed. Please try again.');
                },
                complete: function() {
                    self.resetButtonLoading($form.find('.smart-auth-verify-otp'), 'Verify Code');
                }
            });
        },
        
        /**
         * Handle verification success
         */
        handleVerificationSuccess: function($form, data) {
            this.showSuccessSection($form);
            
            // Redirect after delay
            var redirectUrl = $form.data('redirect-url') || data.redirect_url;
            if (redirectUrl) {
                setTimeout(function() {
                    window.location.href = redirectUrl;
                }, 2000);
            }
        },
        
        /**
         * Resend OTP
         */
        resendOTP: function($button) {
            var $form = $button.closest('.smart-auth-otp-form');
            this.sendOTP($form.find('.smart-auth-send-otp'));
            this.startResendTimer($form);
        },
        
        /**
         * Handle OTP input
         */
        handleOTPInput: function($input) {
            var $form = $input.closest('.smart-auth-otp-form');
            var otpCode = $input.val();
            var expectedLength = $form.data('otp-length') || 6;
            var autoSubmit = $form.data('auto-submit');
            
            // Auto-submit when complete
            if (autoSubmit && otpCode.length === expectedLength) {
                setTimeout(function() {
                    $form.find('.smart-auth-verify-otp').click();
                }, 500);
            }
        },
        
        /**
         * Format phone input
         */
        formatPhoneInput: function($input) {
            var value = $input.val().replace(/\D/g, '');
            $input.val(value);
        },
        
        /**
         * Update country code
         */
        updateCountryCode: function($select) {
            var countryCode = $select.find(':selected').data('code');
            var $phoneInput = $select.closest('.smart-auth-phone-input-wrapper').find('.smart-auth-phone-input');
            
            // Update placeholder if needed
            var placeholder = $phoneInput.attr('placeholder');
            if (placeholder && !placeholder.includes(countryCode)) {
                $phoneInput.attr('placeholder', countryCode + ' ' + placeholder.replace(/^\+\d+\s*/, ''));
            }
        },
        
        /**
         * Show OTP section
         */
        showOTPSection: function($form) {
            $form.find('.smart-auth-phone-section').hide();
            $form.find('.smart-auth-otp-section').show();
            $form.find('.smart-auth-otp-input').focus();
            
            this.startResendTimer($form);
        },
        
        /**
         * Show success section
         */
        showSuccessSection: function($form) {
            $form.find('.smart-auth-otp-section').hide();
            $form.find('.smart-auth-success-section').show();
        },
        
        /**
         * Start resend timer
         */
        startResendTimer: function($form) {
            var self = this;
            var $button = $form.find('.smart-auth-resend-button');
            var $countdown = $button.find('.countdown');
            var cooldown = $form.data('resend-cooldown') || 60;
            
            if (!$form.data('enable-resend')) {
                return;
            }
            
            $button.prop('disabled', true);
            
            var timer = setInterval(function() {
                cooldown--;
                $countdown.text(cooldown);
                
                if (cooldown <= 0) {
                    clearInterval(timer);
                    $button.prop('disabled', false).text('Resend Code');
                }
            }, 1000);
        },
        
        /**
         * Validate phone number
         */
        isValidPhoneNumber: function(phoneNumber) {
            // Basic validation - can be enhanced
            return /^\+?[\d\s\-\(\)]{10,}$/.test(phoneNumber);
        },
        
        /**
         * Format phone number
         */
        formatPhoneNumber: function($form, phoneNumber) {
            var $countrySelect = $form.find('.smart-auth-country-selector');
            var countryCode = '';
            
            if ($countrySelect.length) {
                countryCode = $countrySelect.find(':selected').data('code');
            }
            
            // Remove all non-digits
            var digits = phoneNumber.replace(/\D/g, '');
            
            // Add country code if not present
            if (countryCode && !phoneNumber.startsWith('+')) {
                return countryCode + digits;
            }
            
            return phoneNumber.startsWith('+') ? phoneNumber : '+' + digits;
        },
        
        /**
         * Show error message
         */
        showError: function($form, message) {
            this.showMessage($form, message, 'error');
        },
        
        /**
         * Show success message
         */
        showSuccess: function($form, message) {
            this.showMessage($form, message, 'success');
        },
        
        /**
         * Show message
         */
        showMessage: function($form, message, type) {
            var $messageContainer = $form.find('.smart-auth-message');
            
            if (!$messageContainer.length) {
                $messageContainer = $('<div class="smart-auth-message"></div>');
                $form.prepend($messageContainer);
            }
            
            $messageContainer
                .removeClass('smart-auth-error smart-auth-success')
                .addClass('smart-auth-' + type)
                .html('<p>' + message + '</p>')
                .show();
        },
        
        /**
         * Clear messages
         */
        clearMessages: function($form) {
            $form.find('.smart-auth-message').hide();
        },
        
        /**
         * Set button loading state
         */
        setButtonLoading: function($button, text) {
            $button.prop('disabled', true).text(text).addClass('loading');
        },
        
        /**
         * Reset button loading state
         */
        resetButtonLoading: function($button, text) {
            $button.prop('disabled', false).text(text).removeClass('loading');
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        SmartAuthOTP.init();
    });
    
})(jQuery);
