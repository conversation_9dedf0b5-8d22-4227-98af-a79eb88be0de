<?php
/**
 * Bricks Builder Elements Handler
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Bricks Elements class
 */
class Smart_Auth_Bricks_Elements {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'register_elements'));
    }
    
    /**
     * Register elements
     */
    public function register_elements() {
        // Load element files
        require_once SMART_AUTH_PLUGIN_DIR . 'widgets/bricks/elements/class-auth-form-element.php';
        require_once SMART_AUTH_PLUGIN_DIR . 'widgets/bricks/elements/class-social-login-element.php';
        require_once SMART_AUTH_PLUGIN_DIR . 'widgets/bricks/elements/class-phone-otp-element.php';
        
        // Register elements
        \Bricks\Elements::register_element(SMART_AUTH_PLUGIN_DIR . 'widgets/bricks/elements/class-auth-form-element.php');
        \Bricks\Elements::register_element(SMART_AUTH_PLUGIN_DIR . 'widgets/bricks/elements/class-social-login-element.php');
        \Bricks\Elements::register_element(SMART_AUTH_PLUGIN_DIR . 'widgets/bricks/elements/class-phone-otp-element.php');
    }
}
