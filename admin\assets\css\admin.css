/**
 * Smart Auth Admin Styles
 *
 * @package SmartAuth
 * @since 1.0.0
 */

/* ==========================================================================
   General Admin Styles
   ========================================================================== */

.smart-auth-admin-page {
    max-width: 1200px;
}

.smart-auth-admin-page .nav-tab-wrapper {
    margin-bottom: 20px;
    border-bottom: 1px solid #ccd0d4;
}

.smart-auth-admin-page .nav-tab {
    position: relative;
    display: inline-block;
    padding: 12px 16px;
    margin: 0 4px -1px 0;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.4;
    color: #646970;
    text-decoration: none;
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    border-bottom: none;
    transition: all 0.2s ease;
}

.smart-auth-admin-page .nav-tab:hover {
    background: #fff;
    color: #135e96;
}

.smart-auth-admin-page .nav-tab-active {
    background: #fff;
    color: #135e96;
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
}

.smart-auth-admin-page .tab-content {
    background: #fff;
    padding: 20px;
    border: 1px solid #c3c4c7;
    border-top: none;
}

/* ==========================================================================
   Form Styles
   ========================================================================== */

.smart-auth-admin-page .form-table th {
    width: 200px;
    padding: 20px 10px 20px 0;
    vertical-align: top;
}

.smart-auth-admin-page .form-table td {
    padding: 15px 10px 20px 0;
}

.smart-auth-admin-page .form-table input[type="text"],
.smart-auth-admin-page .form-table input[type="password"],
.smart-auth-admin-page .form-table input[type="number"],
.smart-auth-admin-page .form-table select,
.smart-auth-admin-page .form-table textarea {
    font-size: 14px;
}

.smart-auth-admin-page .form-table .regular-text {
    width: 350px;
}

.smart-auth-admin-page .form-table .large-text {
    width: 500px;
}

.smart-auth-admin-page .form-table .small-text {
    width: 80px;
}

.smart-auth-admin-page .form-table .description {
    margin-top: 8px;
    font-size: 13px;
    color: #646970;
    line-height: 1.5;
}

.smart-auth-admin-page .form-table fieldset {
    margin: 0;
    padding: 0;
    border: none;
}

.smart-auth-admin-page .form-table fieldset label {
    display: block;
    margin-bottom: 8px;
    font-weight: 400;
}

.smart-auth-admin-page .form-table fieldset label input[type="checkbox"] {
    margin-right: 8px;
}

/* ==========================================================================
   Test Connection Buttons
   ========================================================================== */

.smart-auth-test-connection {
    margin-top: 15px;
    padding: 15px 0;
    border-top: 1px solid #dcdcde;
}

.smart-auth-test-connection .button {
    margin-right: 10px;
}

.smart-auth-test-result {
    display: inline-block;
    margin-left: 10px;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 13px;
    font-weight: 500;
}

.smart-auth-test-result.success {
    background: #d1e7dd;
    color: #0f5132;
    border: 1px solid #badbcc;
}

.smart-auth-test-result.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c2c7;
}

/* ==========================================================================
   JWT Secret Generator
   ========================================================================== */

.smart-auth-jwt-secret-section {
    position: relative;
}

.smart-auth-jwt-secret-section input[type="password"] {
    padding-right: 120px;
}

.smart-auth-jwt-secret-section .button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    height: 28px;
    line-height: 26px;
    padding: 0 12px;
    font-size: 12px;
}

/* ==========================================================================
   Security Settings
   ========================================================================== */

.smart-auth-security-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 10px;
}

.smart-auth-security-card {
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.smart-auth-security-card h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
}

.smart-auth-security-card .description {
    margin-top: 5px;
    font-size: 12px;
    color: #666;
}

/* ==========================================================================
   Status Indicators
   ========================================================================== */

.smart-auth-status {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.smart-auth-status.configured {
    background: #d1e7dd;
    color: #0f5132;
}

.smart-auth-status.not-configured {
    background: #f8d7da;
    color: #721c24;
}

.smart-auth-status.warning {
    background: #fff3cd;
    color: #856404;
}

/* ==========================================================================
   Copy to Clipboard
   ========================================================================== */

.smart-auth-copy-wrapper {
    position: relative;
    display: inline-block;
}

.smart-auth-copy-wrapper .copy-to-clipboard {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #646970;
    cursor: pointer;
    font-size: 16px;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.smart-auth-copy-wrapper .copy-to-clipboard:hover {
    background: #f0f0f1;
    color: #135e96;
}

/* ==========================================================================
   Help Text and Documentation
   ========================================================================== */

.smart-auth-help-box {
    background: #f0f6fc;
    border: 1px solid #c9d1d9;
    border-radius: 6px;
    padding: 16px;
    margin: 15px 0;
}

.smart-auth-help-box h4 {
    margin: 0 0 8px 0;
    color: #0969da;
    font-size: 14px;
    font-weight: 600;
}

.smart-auth-help-box p {
    margin: 0 0 8px 0;
    font-size: 13px;
    line-height: 1.5;
}

.smart-auth-help-box p:last-child {
    margin-bottom: 0;
}

.smart-auth-help-box code {
    background: #f6f8fa;
    border: 1px solid #d0d7de;
    border-radius: 3px;
    padding: 2px 4px;
    font-size: 12px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

/* ==========================================================================
   Loading States
   ========================================================================== */

.smart-auth-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.smart-auth-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: smart-auth-spin 1s linear infinite;
}

@keyframes smart-auth-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media screen and (max-width: 782px) {
    .smart-auth-admin-page .nav-tab {
        display: block;
        margin: 0 0 -1px 0;
        border-radius: 0;
    }
    
    .smart-auth-admin-page .form-table th,
    .smart-auth-admin-page .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .smart-auth-admin-page .form-table th {
        border-bottom: none;
    }
    
    .smart-auth-admin-page .form-table .regular-text,
    .smart-auth-admin-page .form-table .large-text {
        width: 100%;
        max-width: 400px;
    }
    
    .smart-auth-security-grid {
        grid-template-columns: 1fr;
    }
}

/* ==========================================================================
   Dark Mode Support
   ========================================================================== */

@media (prefers-color-scheme: dark) {
    .smart-auth-help-box {
        background: #1c2128;
        border-color: #30363d;
        color: #e6edf3;
    }
    
    .smart-auth-help-box h4 {
        color: #58a6ff;
    }
    
    .smart-auth-help-box code {
        background: #262c36;
        border-color: #30363d;
        color: #e6edf3;
    }
    
    .smart-auth-security-card {
        background: #21262d;
        border-color: #30363d;
        color: #e6edf3;
    }
    
    .smart-auth-security-card h4 {
        color: #e6edf3;
    }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .smart-auth-admin-page .nav-tab-wrapper,
    .smart-auth-admin-page .button,
    .smart-auth-test-connection {
        display: none;
    }
    
    .smart-auth-admin-page .tab-content {
        border: none;
        box-shadow: none;
    }
}
