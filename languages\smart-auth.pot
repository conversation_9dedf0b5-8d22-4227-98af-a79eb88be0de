# Smart Auth WordPress Plugin
# Copyright (C) 2024 Flavio
# This file is distributed under the GPL v2 or later.
msgid ""
msgstr ""
"Project-Id-Version: Smart Auth 1.0.0\n"
"Report-Msgid-Bugs-To: https://moflavio.xyz\n"
"POT-Creation-Date: 2024-01-01 00:00+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: WP-CLI 2.8.1\n"
"X-Domain: smart-auth\n"

#: admin/class-admin-settings.php:45
msgid "Smart Auth Settings"
msgstr ""

#: admin/class-admin-settings.php:46
msgid "Smart Auth"
msgstr ""

#: admin/class-admin-settings.php:75
msgid "Firebase"
msgstr ""

#: admin/class-admin-settings.php:78
msgid "Twilio"
msgstr ""

#: admin/class-admin-settings.php:81
msgid "JWT"
msgstr ""

#: admin/class-admin-settings.php:84
msgid "User Settings"
msgstr ""

#: admin/class-admin-settings.php:87
msgid "Security"
msgstr ""

#: admin/class-admin-settings.php:120
msgid "Project ID"
msgstr ""

#: admin/class-admin-settings.php:125
msgid "Your Firebase project ID"
msgstr ""

#: admin/class-admin-settings.php:129
msgid "API Key"
msgstr ""

#: admin/class-admin-settings.php:134
msgid "Your Firebase Web API key"
msgstr ""

#: admin/class-admin-settings.php:138
msgid "Auth Domain"
msgstr ""

#: admin/class-admin-settings.php:143
msgid "Your Firebase auth domain (e.g., project-id.firebaseapp.com)"
msgstr ""

#: admin/class-admin-settings.php:147
msgid "Storage Bucket"
msgstr ""

#: admin/class-admin-settings.php:152
msgid "Your Firebase storage bucket"
msgstr ""

#: admin/class-admin-settings.php:158
msgid "Test Connection"
msgstr ""

#: assets/js/smart-auth.js:35
msgid "Loading..."
msgstr ""

#: assets/js/smart-auth.js:36
msgid "An error occurred. Please try again."
msgstr ""

#: assets/js/smart-auth.js:37
msgid "Success!"
msgstr ""

#: assets/js/smart-auth.js:38
msgid "Please enter a valid phone number."
msgstr ""

#: assets/js/smart-auth.js:39
msgid "OTP sent successfully."
msgstr ""

#: assets/js/smart-auth.js:40
msgid "Invalid OTP code."
msgstr ""

#: smart-auth.php:52
msgid "Smart Auth requires WordPress 5.0 or higher. Please update WordPress."
msgstr ""

#: smart-auth.php:60
msgid "Smart Auth requires PHP 8.0 or higher. Please update PHP."
msgstr ""

#: templates/auth-form.php:25
msgid "Sign In"
msgstr ""

#: templates/auth-form.php:58
msgid "Welcome back!"
msgstr ""

#: templates/auth-form.php:59
msgid "You are logged in as %s"
msgstr ""

#: templates/auth-form.php:61
msgid "Logout"
msgstr ""

#: templates/auth-form.php:78
msgid "Continue with Google"
msgstr ""

#: templates/auth-form.php:80
msgid "Google"
msgstr ""

#: templates/auth-form.php:93
msgid "Continue with Facebook"
msgstr ""

#: templates/auth-form.php:95
msgid "Facebook"
msgstr ""

#: templates/auth-form.php:108
msgid "Continue with Apple"
msgstr ""

#: templates/auth-form.php:110
msgid "Apple"
msgstr ""

#: templates/auth-form.php:119
msgid "Enter your phone number"
msgstr ""

#: templates/auth-form.php:127
msgid "Continue with Phone"
msgstr ""

#: templates/auth-form.php:129
msgid "Phone"
msgstr ""

#: templates/auth-form.php:136
msgid "Authentication providers are not configured. Please contact the site administrator."
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:40
msgid "Smart Auth Form"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:70
msgid "Content"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:76
msgid "Form Title"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:79
msgid "Enter form title"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:84
msgid "Show Providers"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:89
msgid "Google"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:90
msgid "Facebook"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:91
msgid "Apple"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:92
msgid "Phone Number"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:98
msgid "Redirect URL"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:100
msgid "https://your-domain.com"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:108
msgid "Form Style"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:112
msgid "Default"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:113
msgid "Minimal"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:114
msgid "Modern"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:123
msgid "Style"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:129
msgid "Title Color"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:138
msgid "Title Typography"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:145
msgid "Button Color"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:154
msgid "Button Text Color"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:200
msgid "Sign in with"
msgstr ""

#: widgets/elementor/widgets/class-auth-form-widget.php:205
msgid "This is a preview. The actual form will be functional on the frontend."
msgstr ""
