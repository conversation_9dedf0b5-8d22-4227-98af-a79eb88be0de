<?php
/**
 * Plugin Name: Smart Auth
 * Plugin URI: https://moflavio.xyz/smart-auth
 * Description: Complete authentication and user registration using Firebase Authentication with optional Twilio OTP integration. API-first design for mobile app support.
 * Version: 1.0.0
 * Author: Flavio
 * Author URI: https://moflavio.xyz
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: smart-auth
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 8.0
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SMART_AUTH_VERSION', '1.0.0');
define('SMART_AUTH_PLUGIN_FILE', __FILE__);
define('SMART_AUTH_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('SMART_AUTH_PLUGIN_URL', plugin_dir_url(__FILE__));
define('SMART_AUTH_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Autoload Composer dependencies
if (file_exists(SMART_AUTH_PLUGIN_DIR . 'vendor/autoload.php')) {
    require_once SMART_AUTH_PLUGIN_DIR . 'vendor/autoload.php';
}

// Main plugin class
require_once SMART_AUTH_PLUGIN_DIR . 'includes/class-smart-auth.php';

/**
 * Initialize the plugin
 */
function smart_auth_init() {
    // Check if WordPress version is compatible
    if (version_compare(get_bloginfo('version'), '5.0', '<')) {
        add_action('admin_notices', 'smart_auth_wordpress_version_notice');
        return;
    }

    // Check if PHP version is compatible
    if (version_compare(PHP_VERSION, '8.0', '<')) {
        add_action('admin_notices', 'smart_auth_php_version_notice');
        return;
    }

    // Initialize the main plugin class
    Smart_Auth::get_instance();
}

/**
 * WordPress version compatibility notice
 */
function smart_auth_wordpress_version_notice() {
    echo '<div class="notice notice-error"><p>';
    echo esc_html__('Smart Auth requires WordPress 5.0 or higher. Please update WordPress.', 'smart-auth');
    echo '</p></div>';
}

/**
 * PHP version compatibility notice
 */
function smart_auth_php_version_notice() {
    echo '<div class="notice notice-error"><p>';
    echo esc_html__('Smart Auth requires PHP 8.0 or higher. Please update PHP.', 'smart-auth');
    echo '</p></div>';
}

/**
 * Plugin activation hook
 */
function smart_auth_activate() {
    // Create database tables if needed
    Smart_Auth::activate();
}

/**
 * Plugin deactivation hook
 */
function smart_auth_deactivate() {
    Smart_Auth::deactivate();
}

/**
 * Plugin uninstall hook
 */
function smart_auth_uninstall() {
    Smart_Auth::uninstall();
}

// Register hooks
register_activation_hook(__FILE__, 'smart_auth_activate');
register_deactivation_hook(__FILE__, 'smart_auth_deactivate');
register_uninstall_hook(__FILE__, 'smart_auth_uninstall');

// Initialize plugin
add_action('plugins_loaded', 'smart_auth_init');

// Load text domain for translations
add_action('init', function() {
    load_plugin_textdomain('smart-auth', false, dirname(plugin_basename(__FILE__)) . '/languages');
});
