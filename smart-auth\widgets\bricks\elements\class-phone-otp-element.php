<?php
/**
 * Bricks Phone OTP Element
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Phone OTP Verification Element for Bricks Builder
 */
class Smart_Auth_Bricks_Phone_OTP_Element extends \Bricks\Element {
    
    /**
     * Element category
     *
     * @var string
     */
    public $category = 'general';
    
    /**
     * Element name
     *
     * @var string
     */
    public $name = 'smart-auth-phone-otp';
    
    /**
     * Element icon
     *
     * @var string
     */
    public $icon = 'ti-mobile';
    
    /**
     * Element CSS selector
     *
     * @var string
     */
    public $css_selector = '.smart-auth-phone-otp-wrapper';
    
    /**
     * Element scripts
     *
     * @var array
     */
    public $scripts = array('smartAuthPhoneOTP');
    
    /**
     * Element is nestable
     *
     * @var bool
     */
    public $nestable = false;
    
    /**
     * Get element label
     *
     * @return string Element label
     */
    public function get_label() {
        return __('Phone OTP Form', 'smart-auth');
    }
    
    /**
     * Get element keywords
     *
     * @return array Element keywords
     */
    public function get_keywords() {
        return array('phone', 'otp', 'sms', 'verification', 'authentication');
    }
    
    /**
     * Set control groups
     */
    public function set_control_groups() {
        $this->control_groups['content'] = array(
            'title' => __('Content', 'smart-auth'),
            'tab' => 'content',
        );
        
        $this->control_groups['phone_settings'] = array(
            'title' => __('Phone Settings', 'smart-auth'),
            'tab' => 'content',
        );
        
        $this->control_groups['otp_settings'] = array(
            'title' => __('OTP Settings', 'smart-auth'),
            'tab' => 'content',
        );
        
        $this->control_groups['advanced'] = array(
            'title' => __('Advanced', 'smart-auth'),
            'tab' => 'content',
        );
    }
    
    /**
     * Set controls
     */
    public function set_controls() {
        // Content controls
        $this->controls['form_title'] = array(
            'tab' => 'content',
            'group' => 'content',
            'label' => __('Form Title', 'smart-auth'),
            'type' => 'text',
            'default' => __('Phone Verification', 'smart-auth'),
        );
        
        $this->controls['form_description'] = array(
            'tab' => 'content',
            'group' => 'content',
            'label' => __('Form Description', 'smart-auth'),
            'type' => 'textarea',
            'default' => __('Enter your phone number to receive a verification code.', 'smart-auth'),
        );
        
        $this->controls['show_title'] = array(
            'tab' => 'content',
            'group' => 'content',
            'label' => __('Show Title', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
        
        $this->controls['show_description'] = array(
            'tab' => 'content',
            'group' => 'content',
            'label' => __('Show Description', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
        
        // Phone settings controls
        $this->controls['phone_placeholder'] = array(
            'tab' => 'content',
            'group' => 'phone_settings',
            'label' => __('Phone Placeholder', 'smart-auth'),
            'type' => 'text',
            'default' => __('Enter your phone number', 'smart-auth'),
        );
        
        $this->controls['default_country'] = array(
            'tab' => 'content',
            'group' => 'phone_settings',
            'label' => __('Default Country', 'smart-auth'),
            'type' => 'select',
            'options' => array(
                'US' => __('United States', 'smart-auth'),
                'CA' => __('Canada', 'smart-auth'),
                'GB' => __('United Kingdom', 'smart-auth'),
                'AU' => __('Australia', 'smart-auth'),
                'DE' => __('Germany', 'smart-auth'),
                'FR' => __('France', 'smart-auth'),
                'IT' => __('Italy', 'smart-auth'),
                'ES' => __('Spain', 'smart-auth'),
                'BR' => __('Brazil', 'smart-auth'),
                'IN' => __('India', 'smart-auth'),
                'JP' => __('Japan', 'smart-auth'),
            ),
            'default' => 'US',
        );
        
        $this->controls['show_country_selector'] = array(
            'tab' => 'content',
            'group' => 'phone_settings',
            'label' => __('Show Country Selector', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
        
        $this->controls['phone_validation'] = array(
            'tab' => 'content',
            'group' => 'phone_settings',
            'label' => __('Enable Phone Validation', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
        
        // OTP settings controls
        $this->controls['otp_provider'] = array(
            'tab' => 'content',
            'group' => 'otp_settings',
            'label' => __('OTP Provider', 'smart-auth'),
            'type' => 'select',
            'options' => array(
                'firebase' => __('Firebase', 'smart-auth'),
                'twilio' => __('Twilio', 'smart-auth'),
            ),
            'default' => 'firebase',
        );
        
        $this->controls['otp_placeholder'] = array(
            'tab' => 'content',
            'group' => 'otp_settings',
            'label' => __('OTP Placeholder', 'smart-auth'),
            'type' => 'text',
            'default' => __('Enter verification code', 'smart-auth'),
        );
        
        $this->controls['otp_length'] = array(
            'tab' => 'content',
            'group' => 'otp_settings',
            'label' => __('OTP Code Length', 'smart-auth'),
            'type' => 'number',
            'min' => 4,
            'max' => 8,
            'default' => 6,
        );
        
        $this->controls['enable_resend'] = array(
            'tab' => 'content',
            'group' => 'otp_settings',
            'label' => __('Enable Resend', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
        
        $this->controls['resend_cooldown'] = array(
            'tab' => 'content',
            'group' => 'otp_settings',
            'label' => __('Resend Cooldown (seconds)', 'smart-auth'),
            'type' => 'number',
            'min' => 30,
            'max' => 300,
            'default' => 60,
            'required' => array('enable_resend', '=', true),
        );
        
        $this->controls['auto_submit'] = array(
            'tab' => 'content',
            'group' => 'otp_settings',
            'label' => __('Auto Submit on Complete', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
        
        // Advanced controls
        $this->controls['redirect_url'] = array(
            'tab' => 'content',
            'group' => 'advanced',
            'label' => __('Redirect URL', 'smart-auth'),
            'type' => 'text',
            'default' => '',
            'placeholder' => 'https://your-domain.com',
        );
        
        $this->controls['form_style'] = array(
            'tab' => 'content',
            'group' => 'advanced',
            'label' => __('Form Style', 'smart-auth'),
            'type' => 'select',
            'options' => array(
                'default' => __('Default', 'smart-auth'),
                'minimal' => __('Minimal', 'smart-auth'),
                'modern' => __('Modern', 'smart-auth'),
            ),
            'default' => 'default',
        );
        
        $this->controls['enable_loading'] = array(
            'tab' => 'content',
            'group' => 'advanced',
            'label' => __('Enable Loading States', 'smart-auth'),
            'type' => 'checkbox',
            'default' => true,
        );
        
        $this->controls['custom_css_class'] = array(
            'tab' => 'content',
            'group' => 'advanced',
            'label' => __('Custom CSS Class', 'smart-auth'),
            'type' => 'text',
            'default' => '',
        );
    }
    
    /**
     * Enqueue element scripts
     */
    public function enqueue_scripts() {
        wp_enqueue_script(
            'smart-auth-bricks-otp',
            SMART_AUTH_PLUGIN_URL . 'assets/js/bricks-otp.js',
            array('jquery'),
            SMART_AUTH_VERSION,
            true
        );
    }
    
    /**
     * Render element
     */
    public function render() {
        $settings = $this->settings;
        
        // Get element settings
        $form_title = isset($settings['form_title']) ? $settings['form_title'] : __('Phone Verification', 'smart-auth');
        $form_description = isset($settings['form_description']) ? $settings['form_description'] : __('Enter your phone number to receive a verification code.', 'smart-auth');
        $show_title = isset($settings['show_title']) ? $settings['show_title'] : true;
        $show_description = isset($settings['show_description']) ? $settings['show_description'] : true;
        $phone_placeholder = isset($settings['phone_placeholder']) ? $settings['phone_placeholder'] : __('Enter your phone number', 'smart-auth');
        $otp_placeholder = isset($settings['otp_placeholder']) ? $settings['otp_placeholder'] : __('Enter verification code', 'smart-auth');
        $otp_provider = isset($settings['otp_provider']) ? $settings['otp_provider'] : 'firebase';
        $default_country = isset($settings['default_country']) ? $settings['default_country'] : 'US';
        $show_country_selector = isset($settings['show_country_selector']) ? $settings['show_country_selector'] : true;
        $phone_validation = isset($settings['phone_validation']) ? $settings['phone_validation'] : true;
        $otp_length = isset($settings['otp_length']) ? (int) $settings['otp_length'] : 6;
        $enable_resend = isset($settings['enable_resend']) ? $settings['enable_resend'] : true;
        $resend_cooldown = isset($settings['resend_cooldown']) ? (int) $settings['resend_cooldown'] : 60;
        $auto_submit = isset($settings['auto_submit']) ? $settings['auto_submit'] : true;
        $redirect_url = isset($settings['redirect_url']) ? $settings['redirect_url'] : '';
        $form_style = isset($settings['form_style']) ? $settings['form_style'] : 'default';
        $enable_loading = isset($settings['enable_loading']) ? $settings['enable_loading'] : true;
        $custom_css_class = isset($settings['custom_css_class']) ? $settings['custom_css_class'] : '';
        
        // Set wrapper attributes
        $wrapper_classes = array(
            'smart-auth-phone-otp-wrapper',
            'form-style-' . $form_style,
        );
        
        if (!empty($custom_css_class)) {
            $wrapper_classes[] = $custom_css_class;
        }
        
        if ($enable_loading) {
            $wrapper_classes[] = 'enable-loading';
        }
        
        $this->set_attribute('wrapper', 'class', implode(' ', $wrapper_classes));
        
        // Build data attributes
        $data_attributes = array(
            'data-provider' => $otp_provider,
            'data-default-country' => $default_country,
            'data-otp-length' => $otp_length,
            'data-resend-cooldown' => $resend_cooldown,
            'data-redirect-url' => $redirect_url,
        );
        
        if ($show_country_selector) {
            $data_attributes['data-show-country-selector'] = 'true';
        }
        
        if ($phone_validation) {
            $data_attributes['data-phone-validation'] = 'true';
        }
        
        if ($enable_resend) {
            $data_attributes['data-enable-resend'] = 'true';
        }
        
        if ($auto_submit) {
            $data_attributes['data-auto-submit'] = 'true';
        }
        
        foreach ($data_attributes as $attr => $value) {
            $this->set_attribute('wrapper', $attr, $value);
        }
        
        // Build shortcode attributes
        $shortcode_atts = array(
            'provider="' . esc_attr($otp_provider) . '"',
            'default_country="' . esc_attr($default_country) . '"',
            'phone_placeholder="' . esc_attr($phone_placeholder) . '"',
            'otp_placeholder="' . esc_attr($otp_placeholder) . '"',
            'otp_length="' . esc_attr($otp_length) . '"',
            'resend_cooldown="' . esc_attr($resend_cooldown) . '"',
            'form_style="' . esc_attr($form_style) . '"',
            'show_country_selector="' . ($show_country_selector ? 'true' : 'false') . '"',
            'phone_validation="' . ($phone_validation ? 'true' : 'false') . '"',
            'enable_resend="' . ($enable_resend ? 'true' : 'false') . '"',
            'auto_submit="' . ($auto_submit ? 'true' : 'false') . '"',
        );
        
        if ($show_title && !empty($form_title)) {
            $shortcode_atts[] = 'title="' . esc_attr($form_title) . '"';
        }
        
        if ($show_description && !empty($form_description)) {
            $shortcode_atts[] = 'description="' . esc_attr($form_description) . '"';
        }
        
        if (!empty($redirect_url)) {
            $shortcode_atts[] = 'redirect_url="' . esc_url($redirect_url) . '"';
        }
        
        // Render element
        echo "<div {$this->render_attributes('wrapper')}>";
        echo do_shortcode('[smart_auth_otp ' . implode(' ', $shortcode_atts) . ']');
        echo '</div>';
    }
}
