<?php
/**
 * Admin Settings Handler
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin Settings class
 */
class Smart_Auth_Admin_Settings {
    
    /**
     * Settings page hook suffix
     *
     * @var string
     */
    private $page_hook;
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('wp_ajax_smart_auth_test_firebase', array($this, 'ajax_test_firebase'));
        add_action('wp_ajax_smart_auth_test_twilio', array($this, 'ajax_test_twilio'));
        add_action('wp_ajax_smart_auth_generate_jwt_secret', array($this, 'ajax_generate_jwt_secret'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        $this->page_hook = add_options_page(
            __('Smart Auth Settings', 'smart-auth'),
            __('Smart Auth', 'smart-auth'),
            'manage_options',
            'smart-auth-settings',
            array($this, 'render_settings_page')
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        // Firebase settings
        register_setting('smart_auth_firebase', 'smart_auth_firebase_settings', array(
            'sanitize_callback' => array($this, 'sanitize_firebase_settings'),
        ));
        
        // Twilio settings
        register_setting('smart_auth_twilio', 'smart_auth_twilio_settings', array(
            'sanitize_callback' => array($this, 'sanitize_twilio_settings'),
        ));
        
        // JWT settings
        register_setting('smart_auth_jwt', 'smart_auth_jwt_settings', array(
            'sanitize_callback' => array($this, 'sanitize_jwt_settings'),
        ));
        
        // User settings
        register_setting('smart_auth_user', 'smart_auth_user_settings', array(
            'sanitize_callback' => array($this, 'sanitize_user_settings'),
        ));
        
        // Security settings
        register_setting('smart_auth_security', 'smart_auth_security_settings', array(
            'sanitize_callback' => array($this, 'sanitize_security_settings'),
        ));
    }
    
    /**
     * Render settings page
     */
    public function render_settings_page() {
        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'firebase';
        ?>
        <div class="wrap">
            <h1><?php echo esc_html__('Smart Auth Settings', 'smart-auth'); ?></h1>
            
            <nav class="nav-tab-wrapper">
                <a href="?page=smart-auth-settings&tab=firebase" class="nav-tab <?php echo $active_tab === 'firebase' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('Firebase', 'smart-auth'); ?>
                </a>
                <a href="?page=smart-auth-settings&tab=twilio" class="nav-tab <?php echo $active_tab === 'twilio' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('Twilio', 'smart-auth'); ?>
                </a>
                <a href="?page=smart-auth-settings&tab=jwt" class="nav-tab <?php echo $active_tab === 'jwt' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('JWT', 'smart-auth'); ?>
                </a>
                <a href="?page=smart-auth-settings&tab=user" class="nav-tab <?php echo $active_tab === 'user' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('User Settings', 'smart-auth'); ?>
                </a>
                <a href="?page=smart-auth-settings&tab=security" class="nav-tab <?php echo $active_tab === 'security' ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e('Security', 'smart-auth'); ?>
                </a>
            </nav>
            
            <div class="tab-content">
                <?php
                switch ($active_tab) {
                    case 'firebase':
                        $this->render_firebase_tab();
                        break;
                    case 'twilio':
                        $this->render_twilio_tab();
                        break;
                    case 'jwt':
                        $this->render_jwt_tab();
                        break;
                    case 'user':
                        $this->render_user_tab();
                        break;
                    case 'security':
                        $this->render_security_tab();
                        break;
                    default:
                        $this->render_firebase_tab();
                        break;
                }
                ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render Firebase settings tab
     */
    private function render_firebase_tab() {
        $settings = get_option('smart_auth_firebase_settings', array());
        ?>
        <form method="post" action="options.php">
            <?php
            settings_fields('smart_auth_firebase');
            do_settings_sections('smart_auth_firebase');
            ?>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php esc_html_e('Project ID', 'smart-auth'); ?></th>
                    <td>
                        <input type="text" name="smart_auth_firebase_settings[project_id]" 
                               value="<?php echo esc_attr(isset($settings['project_id']) ? $settings['project_id'] : ''); ?>" 
                               class="regular-text" />
                        <p class="description"><?php esc_html_e('Your Firebase project ID', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('API Key', 'smart-auth'); ?></th>
                    <td>
                        <input type="text" name="smart_auth_firebase_settings[api_key]" 
                               value="<?php echo esc_attr(isset($settings['api_key']) ? $settings['api_key'] : ''); ?>" 
                               class="regular-text" />
                        <p class="description"><?php esc_html_e('Your Firebase Web API key', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Auth Domain', 'smart-auth'); ?></th>
                    <td>
                        <input type="text" name="smart_auth_firebase_settings[auth_domain]" 
                               value="<?php echo esc_attr(isset($settings['auth_domain']) ? $settings['auth_domain'] : ''); ?>" 
                               class="regular-text" />
                        <p class="description"><?php esc_html_e('Your Firebase auth domain (e.g., project-id.firebaseapp.com)', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Storage Bucket', 'smart-auth'); ?></th>
                    <td>
                        <input type="text" name="smart_auth_firebase_settings[storage_bucket]" 
                               value="<?php echo esc_attr(isset($settings['storage_bucket']) ? $settings['storage_bucket'] : ''); ?>" 
                               class="regular-text" />
                        <p class="description"><?php esc_html_e('Your Firebase storage bucket', 'smart-auth'); ?></p>
                    </td>
                </tr>
            </table>
            
            <p>
                <button type="button" id="test-firebase-connection" class="button button-secondary">
                    <?php esc_html_e('Test Connection', 'smart-auth'); ?>
                </button>
                <span id="firebase-test-result"></span>
            </p>
            
            <?php submit_button(); ?>
        </form>
        <?php
    }
    
    /**
     * Render Twilio settings tab
     */
    private function render_twilio_tab() {
        $settings = get_option('smart_auth_twilio_settings', array());
        ?>
        <form method="post" action="options.php">
            <?php
            settings_fields('smart_auth_twilio');
            do_settings_sections('smart_auth_twilio');
            ?>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php esc_html_e('Account SID', 'smart-auth'); ?></th>
                    <td>
                        <input type="text" name="smart_auth_twilio_settings[account_sid]"
                               value="<?php echo esc_attr(isset($settings['account_sid']) ? $settings['account_sid'] : ''); ?>"
                               class="regular-text" />
                        <p class="description"><?php esc_html_e('Your Twilio Account SID', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Auth Token', 'smart-auth'); ?></th>
                    <td>
                        <input type="password" name="smart_auth_twilio_settings[auth_token]"
                               value="<?php echo esc_attr(isset($settings['auth_token']) ? $settings['auth_token'] : ''); ?>"
                               class="regular-text" />
                        <p class="description"><?php esc_html_e('Your Twilio Auth Token', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Verify Service SID', 'smart-auth'); ?></th>
                    <td>
                        <input type="text" name="smart_auth_twilio_settings[verify_service_sid]"
                               value="<?php echo esc_attr(isset($settings['verify_service_sid']) ? $settings['verify_service_sid'] : ''); ?>"
                               class="regular-text" />
                        <p class="description"><?php esc_html_e('Your Twilio Verify Service SID', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Default Region', 'smart-auth'); ?></th>
                    <td>
                        <select name="smart_auth_twilio_settings[default_region]">
                            <?php
                            $regions = array(
                                'US' => __('United States', 'smart-auth'),
                                'CA' => __('Canada', 'smart-auth'),
                                'GB' => __('United Kingdom', 'smart-auth'),
                                'AU' => __('Australia', 'smart-auth'),
                                'DE' => __('Germany', 'smart-auth'),
                                'FR' => __('France', 'smart-auth'),
                                'IT' => __('Italy', 'smart-auth'),
                                'ES' => __('Spain', 'smart-auth'),
                                'BR' => __('Brazil', 'smart-auth'),
                                'IN' => __('India', 'smart-auth'),
                                'JP' => __('Japan', 'smart-auth'),
                            );
                            $selected_region = isset($settings['default_region']) ? $settings['default_region'] : 'US';
                            foreach ($regions as $code => $name) {
                                echo '<option value="' . esc_attr($code) . '"' . selected($selected_region, $code, false) . '>' . esc_html($name) . '</option>';
                            }
                            ?>
                        </select>
                        <p class="description"><?php esc_html_e('Default region for phone number parsing', 'smart-auth'); ?></p>
                    </td>
                </tr>
            </table>

            <p>
                <button type="button" id="test-twilio-connection" class="button button-secondary">
                    <?php esc_html_e('Test Connection', 'smart-auth'); ?>
                </button>
                <span id="twilio-test-result"></span>
            </p>

            <?php submit_button(); ?>
        </form>
        <?php
    }
    
    /**
     * Render JWT settings tab
     */
    private function render_jwt_tab() {
        $settings = get_option('smart_auth_jwt_settings', array());
        ?>
        <form method="post" action="options.php">
            <?php
            settings_fields('smart_auth_jwt');
            do_settings_sections('smart_auth_jwt');
            ?>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php esc_html_e('Secret Key', 'smart-auth'); ?></th>
                    <td>
                        <input type="password" name="smart_auth_jwt_settings[secret_key]"
                               value="<?php echo esc_attr(isset($settings['secret_key']) ? $settings['secret_key'] : ''); ?>"
                               class="large-text" readonly />
                        <button type="button" id="generate-jwt-secret" class="button button-secondary">
                            <?php esc_html_e('Generate New Secret', 'smart-auth'); ?>
                        </button>
                        <p class="description">
                            <?php esc_html_e('Secret key for JWT token signing. Keep this secure and never share it.', 'smart-auth'); ?>
                            <br>
                            <?php if (defined('JWT_AUTH_SECRET_KEY')) : ?>
                                <strong><?php esc_html_e('Note: JWT Authentication plugin detected. Its secret key will be used if this field is empty.', 'smart-auth'); ?></strong>
                            <?php endif; ?>
                        </p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Token Expiration', 'smart-auth'); ?></th>
                    <td>
                        <input type="number" name="smart_auth_jwt_settings[expiration]"
                               value="<?php echo esc_attr(isset($settings['expiration']) ? $settings['expiration'] / HOUR_IN_SECONDS : 24); ?>"
                               min="1" max="168" class="small-text" />
                        <span><?php esc_html_e('hours', 'smart-auth'); ?></span>
                        <p class="description"><?php esc_html_e('How long JWT tokens remain valid (1-168 hours)', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Refresh Window', 'smart-auth'); ?></th>
                    <td>
                        <input type="number" name="smart_auth_jwt_settings[refresh_window]"
                               value="<?php echo esc_attr(isset($settings['refresh_window']) ? $settings['refresh_window'] / DAY_IN_SECONDS : 7); ?>"
                               min="1" max="30" class="small-text" />
                        <span><?php esc_html_e('days', 'smart-auth'); ?></span>
                        <p class="description"><?php esc_html_e('How long expired tokens can be refreshed (1-30 days)', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Algorithm', 'smart-auth'); ?></th>
                    <td>
                        <select name="smart_auth_jwt_settings[algorithm]">
                            <?php
                            $algorithms = array(
                                'HS256' => 'HS256 (HMAC SHA-256)',
                                'HS384' => 'HS384 (HMAC SHA-384)',
                                'HS512' => 'HS512 (HMAC SHA-512)',
                            );
                            $selected_algorithm = isset($settings['algorithm']) ? $settings['algorithm'] : 'HS256';
                            foreach ($algorithms as $value => $label) {
                                echo '<option value="' . esc_attr($value) . '"' . selected($selected_algorithm, $value, false) . '>' . esc_html($label) . '</option>';
                            }
                            ?>
                        </select>
                        <p class="description"><?php esc_html_e('JWT signing algorithm', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Enable Token Storage', 'smart-auth'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="smart_auth_jwt_settings[enable_token_storage]" value="1"
                                   <?php checked(isset($settings['enable_token_storage']) ? $settings['enable_token_storage'] : false); ?> />
                            <?php esc_html_e('Store token hashes for additional security', 'smart-auth'); ?>
                        </label>
                        <p class="description"><?php esc_html_e('Stores token hashes in database for validation. Slightly reduces performance but increases security.', 'smart-auth'); ?></p>
                    </td>
                </tr>
            </table>

            <?php submit_button(); ?>
        </form>
        <?php
    }
    
    /**
     * Render User settings tab
     */
    private function render_user_tab() {
        $settings = get_option('smart_auth_user_settings', array());
        ?>
        <form method="post" action="options.php">
            <?php
            settings_fields('smart_auth_user');
            do_settings_sections('smart_auth_user');
            ?>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php esc_html_e('Auto Create Users', 'smart-auth'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="smart_auth_user_settings[auto_create_users]" value="1"
                                   <?php checked(isset($settings['auto_create_users']) ? $settings['auto_create_users'] : true); ?> />
                            <?php esc_html_e('Automatically create WordPress users for new Firebase users', 'smart-auth'); ?>
                        </label>
                        <p class="description"><?php esc_html_e('If disabled, only existing WordPress users can log in.', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Default User Role', 'smart-auth'); ?></th>
                    <td>
                        <select name="smart_auth_user_settings[default_role]">
                            <?php
                            $roles = get_editable_roles();
                            $selected_role = isset($settings['default_role']) ? $settings['default_role'] : 'subscriber';
                            foreach ($roles as $role_name => $role_info) {
                                echo '<option value="' . esc_attr($role_name) . '"' . selected($selected_role, $role_name, false) . '>' . esc_html($role_info['name']) . '</option>';
                            }
                            ?>
                        </select>
                        <p class="description"><?php esc_html_e('Default role assigned to new users', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Duplicate Account Strategy', 'smart-auth'); ?></th>
                    <td>
                        <select name="smart_auth_user_settings[duplicate_strategy]">
                            <option value="login" <?php selected(isset($settings['duplicate_strategy']) ? $settings['duplicate_strategy'] : 'login', 'login'); ?>>
                                <?php esc_html_e('Login to existing account', 'smart-auth'); ?>
                            </option>
                            <option value="merge" <?php selected(isset($settings['duplicate_strategy']) ? $settings['duplicate_strategy'] : 'login', 'merge'); ?>>
                                <?php esc_html_e('Merge Firebase data with existing account', 'smart-auth'); ?>
                            </option>
                            <option value="error" <?php selected(isset($settings['duplicate_strategy']) ? $settings['duplicate_strategy'] : 'login', 'error'); ?>>
                                <?php esc_html_e('Show error message', 'smart-auth'); ?>
                            </option>
                        </select>
                        <p class="description"><?php esc_html_e('What to do when a user tries to register with an email/phone that already exists', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Profile Synchronization', 'smart-auth'); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" name="smart_auth_user_settings[sync_display_name]" value="1"
                                       <?php checked(isset($settings['sync_display_name']) ? $settings['sync_display_name'] : true); ?> />
                                <?php esc_html_e('Sync display name from Firebase', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" name="smart_auth_user_settings[sync_email]" value="1"
                                       <?php checked(isset($settings['sync_email']) ? $settings['sync_email'] : false); ?> />
                                <?php esc_html_e('Sync email from Firebase (only if verified)', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" name="smart_auth_user_settings[sync_profile_picture]" value="1"
                                       <?php checked(isset($settings['sync_profile_picture']) ? $settings['sync_profile_picture'] : true); ?> />
                                <?php esc_html_e('Sync profile picture from Firebase', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" name="smart_auth_user_settings[download_profile_picture]" value="1"
                                       <?php checked(isset($settings['download_profile_picture']) ? $settings['download_profile_picture'] : false); ?> />
                                <?php esc_html_e('Download and store profile pictures locally', 'smart-auth'); ?>
                            </label>
                        </fieldset>
                        <p class="description"><?php esc_html_e('Choose which user data to synchronize from Firebase', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('User Meta Storage', 'smart-auth'); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" name="smart_auth_user_settings[store_firebase_uid]" value="1"
                                       <?php checked(isset($settings['store_firebase_uid']) ? $settings['store_firebase_uid'] : true); ?> />
                                <?php esc_html_e('Store Firebase UID', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" name="smart_auth_user_settings[store_auth_provider]" value="1"
                                       <?php checked(isset($settings['store_auth_provider']) ? $settings['store_auth_provider'] : true); ?> />
                                <?php esc_html_e('Store authentication provider', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" name="smart_auth_user_settings[store_phone_number]" value="1"
                                       <?php checked(isset($settings['store_phone_number']) ? $settings['store_phone_number'] : true); ?> />
                                <?php esc_html_e('Store phone number', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" name="smart_auth_user_settings[store_custom_claims]" value="1"
                                       <?php checked(isset($settings['store_custom_claims']) ? $settings['store_custom_claims'] : false); ?> />
                                <?php esc_html_e('Store Firebase custom claims', 'smart-auth'); ?>
                            </label>
                        </fieldset>
                        <p class="description"><?php esc_html_e('Choose which Firebase data to store in WordPress user meta', 'smart-auth'); ?></p>
                    </td>
                </tr>
            </table>

            <?php submit_button(); ?>
        </form>
        <?php
    }
    
    /**
     * Render Security settings tab
     */
    private function render_security_tab() {
        $settings = get_option('smart_auth_security_settings', array());
        ?>
        <form method="post" action="options.php">
            <?php
            settings_fields('smart_auth_security');
            do_settings_sections('smart_auth_security');
            ?>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php esc_html_e('Rate Limiting', 'smart-auth'); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <?php esc_html_e('Max attempts:', 'smart-auth'); ?>
                                <input type="number" name="smart_auth_security_settings[rate_limit_attempts]"
                                       value="<?php echo esc_attr(isset($settings['rate_limit_attempts']) ? $settings['rate_limit_attempts'] : 3); ?>"
                                       min="1" max="10" class="small-text" />
                            </label><br>
                            <label>
                                <?php esc_html_e('Time window:', 'smart-auth'); ?>
                                <input type="number" name="smart_auth_security_settings[rate_limit_window]"
                                       value="<?php echo esc_attr(isset($settings['rate_limit_window']) ? $settings['rate_limit_window'] / 60 : 15); ?>"
                                       min="5" max="60" class="small-text" />
                                <?php esc_html_e('minutes', 'smart-auth'); ?>
                            </label>
                        </fieldset>
                        <p class="description"><?php esc_html_e('Limit OTP requests per IP address', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('OTP Settings', 'smart-auth'); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <?php esc_html_e('Resend cooldown:', 'smart-auth'); ?>
                                <input type="number" name="smart_auth_security_settings[otp_resend_cooldown]"
                                       value="<?php echo esc_attr(isset($settings['otp_resend_cooldown']) ? $settings['otp_resend_cooldown'] : 60); ?>"
                                       min="30" max="300" class="small-text" />
                                <?php esc_html_e('seconds', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <?php esc_html_e('OTP expiration:', 'smart-auth'); ?>
                                <input type="number" name="smart_auth_security_settings[otp_expiration]"
                                       value="<?php echo esc_attr(isset($settings['otp_expiration']) ? $settings['otp_expiration'] / 60 : 10); ?>"
                                       min="5" max="30" class="small-text" />
                                <?php esc_html_e('minutes', 'smart-auth'); ?>
                            </label>
                        </fieldset>
                        <p class="description"><?php esc_html_e('OTP security settings', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Failed Login Protection', 'smart-auth'); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" name="smart_auth_security_settings[enable_failed_login_protection]" value="1"
                                       <?php checked(isset($settings['enable_failed_login_protection']) ? $settings['enable_failed_login_protection'] : true); ?> />
                                <?php esc_html_e('Enable failed login protection', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <?php esc_html_e('Max failed attempts:', 'smart-auth'); ?>
                                <input type="number" name="smart_auth_security_settings[max_failed_attempts]"
                                       value="<?php echo esc_attr(isset($settings['max_failed_attempts']) ? $settings['max_failed_attempts'] : 5); ?>"
                                       min="3" max="20" class="small-text" />
                            </label><br>
                            <label>
                                <?php esc_html_e('Lockout duration:', 'smart-auth'); ?>
                                <input type="number" name="smart_auth_security_settings[lockout_duration]"
                                       value="<?php echo esc_attr(isset($settings['lockout_duration']) ? $settings['lockout_duration'] / 60 : 30); ?>"
                                       min="15" max="1440" class="small-text" />
                                <?php esc_html_e('minutes', 'smart-auth'); ?>
                            </label>
                        </fieldset>
                        <p class="description"><?php esc_html_e('Protect against brute force attacks', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('IP Blocking', 'smart-auth'); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" name="smart_auth_security_settings[enable_ip_blocking]" value="1"
                                       <?php checked(isset($settings['enable_ip_blocking']) ? $settings['enable_ip_blocking'] : false); ?> />
                                <?php esc_html_e('Enable IP blocking', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <?php esc_html_e('Blocked IPs (one per line):', 'smart-auth'); ?><br>
                                <textarea name="smart_auth_security_settings[blocked_ips]" rows="5" cols="50" class="large-text"><?php echo esc_textarea(isset($settings['blocked_ips']) ? $settings['blocked_ips'] : ''); ?></textarea>
                            </label>
                        </fieldset>
                        <p class="description"><?php esc_html_e('Block specific IP addresses from authentication', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('CAPTCHA Integration', 'smart-auth'); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" name="smart_auth_security_settings[enable_captcha]" value="1"
                                       <?php checked(isset($settings['enable_captcha']) ? $settings['enable_captcha'] : false); ?> />
                                <?php esc_html_e('Enable CAPTCHA for phone authentication', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <?php esc_html_e('CAPTCHA provider:', 'smart-auth'); ?>
                                <select name="smart_auth_security_settings[captcha_provider]">
                                    <option value="recaptcha" <?php selected(isset($settings['captcha_provider']) ? $settings['captcha_provider'] : 'recaptcha', 'recaptcha'); ?>>
                                        <?php esc_html_e('Google reCAPTCHA', 'smart-auth'); ?>
                                    </option>
                                    <option value="hcaptcha" <?php selected(isset($settings['captcha_provider']) ? $settings['captcha_provider'] : 'recaptcha', 'hcaptcha'); ?>>
                                        <?php esc_html_e('hCaptcha', 'smart-auth'); ?>
                                    </option>
                                </select>
                            </label><br>
                            <label>
                                <?php esc_html_e('Site key:', 'smart-auth'); ?>
                                <input type="text" name="smart_auth_security_settings[captcha_site_key]"
                                       value="<?php echo esc_attr(isset($settings['captcha_site_key']) ? $settings['captcha_site_key'] : ''); ?>"
                                       class="regular-text" />
                            </label><br>
                            <label>
                                <?php esc_html_e('Secret key:', 'smart-auth'); ?>
                                <input type="password" name="smart_auth_security_settings[captcha_secret_key]"
                                       value="<?php echo esc_attr(isset($settings['captcha_secret_key']) ? $settings['captcha_secret_key'] : ''); ?>"
                                       class="regular-text" />
                            </label>
                        </fieldset>
                        <p class="description"><?php esc_html_e('Add CAPTCHA verification to prevent automated attacks', 'smart-auth'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><?php esc_html_e('Security Headers', 'smart-auth'); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" name="smart_auth_security_settings[enable_security_headers]" value="1"
                                       <?php checked(isset($settings['enable_security_headers']) ? $settings['enable_security_headers'] : true); ?> />
                                <?php esc_html_e('Enable security headers', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" name="smart_auth_security_settings[enable_csrf_protection]" value="1"
                                       <?php checked(isset($settings['enable_csrf_protection']) ? $settings['enable_csrf_protection'] : true); ?> />
                                <?php esc_html_e('Enable CSRF protection', 'smart-auth'); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" name="smart_auth_security_settings[enable_https_only]" value="1"
                                       <?php checked(isset($settings['enable_https_only']) ? $settings['enable_https_only'] : true); ?> />
                                <?php esc_html_e('Require HTTPS for authentication', 'smart-auth'); ?>
                            </label>
                        </fieldset>
                        <p class="description"><?php esc_html_e('Additional security measures', 'smart-auth'); ?></p>
                    </td>
                </tr>
            </table>

            <?php submit_button(); ?>
        </form>
        <?php
    }
    
    /**
     * Sanitize Firebase settings
     */
    public function sanitize_firebase_settings($input) {
        $sanitized = array();
        
        if (isset($input['project_id'])) {
            $sanitized['project_id'] = sanitize_text_field($input['project_id']);
        }
        
        if (isset($input['api_key'])) {
            $sanitized['api_key'] = sanitize_text_field($input['api_key']);
        }
        
        if (isset($input['auth_domain'])) {
            $sanitized['auth_domain'] = sanitize_text_field($input['auth_domain']);
        }
        
        if (isset($input['storage_bucket'])) {
            $sanitized['storage_bucket'] = sanitize_text_field($input['storage_bucket']);
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize Twilio settings
     */
    public function sanitize_twilio_settings($input) {
        $sanitized = array();

        if (isset($input['account_sid'])) {
            $sanitized['account_sid'] = sanitize_text_field($input['account_sid']);
        }

        if (isset($input['auth_token'])) {
            $sanitized['auth_token'] = sanitize_text_field($input['auth_token']);
        }

        if (isset($input['verify_service_sid'])) {
            $sanitized['verify_service_sid'] = sanitize_text_field($input['verify_service_sid']);
        }

        if (isset($input['default_region'])) {
            $sanitized['default_region'] = sanitize_text_field($input['default_region']);
        }

        return $sanitized;
    }

    /**
     * Sanitize JWT settings
     */
    public function sanitize_jwt_settings($input) {
        $sanitized = array();

        if (isset($input['secret_key'])) {
            $sanitized['secret_key'] = sanitize_text_field($input['secret_key']);
        }

        if (isset($input['expiration'])) {
            $hours = (int) $input['expiration'];
            $sanitized['expiration'] = max(1, min(168, $hours)) * HOUR_IN_SECONDS;
        }

        if (isset($input['refresh_window'])) {
            $days = (int) $input['refresh_window'];
            $sanitized['refresh_window'] = max(1, min(30, $days)) * DAY_IN_SECONDS;
        }

        if (isset($input['algorithm'])) {
            $allowed_algorithms = array('HS256', 'HS384', 'HS512');
            $sanitized['algorithm'] = in_array($input['algorithm'], $allowed_algorithms) ? $input['algorithm'] : 'HS256';
        }

        if (isset($input['enable_token_storage'])) {
            $sanitized['enable_token_storage'] = (bool) $input['enable_token_storage'];
        }

        return $sanitized;
    }

    /**
     * Sanitize user settings
     */
    public function sanitize_user_settings($input) {
        $sanitized = array();

        if (isset($input['auto_create_users'])) {
            $sanitized['auto_create_users'] = (bool) $input['auto_create_users'];
        }

        if (isset($input['default_role'])) {
            $roles = get_editable_roles();
            $sanitized['default_role'] = array_key_exists($input['default_role'], $roles) ? $input['default_role'] : 'subscriber';
        }

        if (isset($input['duplicate_strategy'])) {
            $allowed_strategies = array('login', 'merge', 'error');
            $sanitized['duplicate_strategy'] = in_array($input['duplicate_strategy'], $allowed_strategies) ? $input['duplicate_strategy'] : 'login';
        }

        // Profile sync options
        $sync_options = array('sync_display_name', 'sync_email', 'sync_profile_picture', 'download_profile_picture');
        foreach ($sync_options as $option) {
            if (isset($input[$option])) {
                $sanitized[$option] = (bool) $input[$option];
            }
        }

        // User meta storage options
        $meta_options = array('store_firebase_uid', 'store_auth_provider', 'store_phone_number', 'store_custom_claims');
        foreach ($meta_options as $option) {
            if (isset($input[$option])) {
                $sanitized[$option] = (bool) $input[$option];
            }
        }

        return $sanitized;
    }

    /**
     * Sanitize security settings
     */
    public function sanitize_security_settings($input) {
        $sanitized = array();

        if (isset($input['rate_limit_attempts'])) {
            $sanitized['rate_limit_attempts'] = max(1, min(10, (int) $input['rate_limit_attempts']));
        }

        if (isset($input['rate_limit_window'])) {
            $minutes = max(5, min(60, (int) $input['rate_limit_window']));
            $sanitized['rate_limit_window'] = $minutes * 60;
        }

        if (isset($input['otp_resend_cooldown'])) {
            $sanitized['otp_resend_cooldown'] = max(30, min(300, (int) $input['otp_resend_cooldown']));
        }

        if (isset($input['otp_expiration'])) {
            $minutes = max(5, min(30, (int) $input['otp_expiration']));
            $sanitized['otp_expiration'] = $minutes * 60;
        }

        if (isset($input['enable_failed_login_protection'])) {
            $sanitized['enable_failed_login_protection'] = (bool) $input['enable_failed_login_protection'];
        }

        if (isset($input['max_failed_attempts'])) {
            $sanitized['max_failed_attempts'] = max(3, min(20, (int) $input['max_failed_attempts']));
        }

        if (isset($input['lockout_duration'])) {
            $minutes = max(15, min(1440, (int) $input['lockout_duration']));
            $sanitized['lockout_duration'] = $minutes * 60;
        }

        if (isset($input['enable_ip_blocking'])) {
            $sanitized['enable_ip_blocking'] = (bool) $input['enable_ip_blocking'];
        }

        if (isset($input['blocked_ips'])) {
            $ips = explode("\n", $input['blocked_ips']);
            $valid_ips = array();
            foreach ($ips as $ip) {
                $ip = trim($ip);
                if (!empty($ip) && filter_var($ip, FILTER_VALIDATE_IP)) {
                    $valid_ips[] = $ip;
                }
            }
            $sanitized['blocked_ips'] = implode("\n", $valid_ips);
        }

        if (isset($input['enable_captcha'])) {
            $sanitized['enable_captcha'] = (bool) $input['enable_captcha'];
        }

        if (isset($input['captcha_provider'])) {
            $allowed_providers = array('recaptcha', 'hcaptcha');
            $sanitized['captcha_provider'] = in_array($input['captcha_provider'], $allowed_providers) ? $input['captcha_provider'] : 'recaptcha';
        }

        if (isset($input['captcha_site_key'])) {
            $sanitized['captcha_site_key'] = sanitize_text_field($input['captcha_site_key']);
        }

        if (isset($input['captcha_secret_key'])) {
            $sanitized['captcha_secret_key'] = sanitize_text_field($input['captcha_secret_key']);
        }

        $security_options = array('enable_security_headers', 'enable_csrf_protection', 'enable_https_only');
        foreach ($security_options as $option) {
            if (isset($input[$option])) {
                $sanitized[$option] = (bool) $input[$option];
            }
        }

        return $sanitized;
    }

    /**
     * AJAX handler for testing Firebase connection
     */
    public function ajax_test_firebase() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'smart_auth_admin_nonce')) {
            wp_die(__('Security check failed.', 'smart-auth'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'smart-auth'));
        }

        // Get Firebase handler
        $firebase_auth = new Smart_Auth_Firebase();

        // Test connection
        $result = $firebase_auth->test_connection();

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message(),
            ));
        } else {
            wp_send_json_success(array(
                'message' => __('Firebase connection successful!', 'smart-auth'),
            ));
        }
    }

    /**
     * AJAX handler for testing Twilio connection
     */
    public function ajax_test_twilio() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'smart_auth_admin_nonce')) {
            wp_die(__('Security check failed.', 'smart-auth'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'smart-auth'));
        }

        // Get Twilio handler
        $twilio_auth = new Smart_Auth_Twilio();

        // Test connection
        $result = $twilio_auth->test_connection();

        if (is_wp_error($result)) {
            wp_send_json_error(array(
                'message' => $result->get_error_message(),
            ));
        } else {
            wp_send_json_success(array(
                'message' => __('Twilio connection successful!', 'smart-auth'),
            ));
        }
    }

    /**
     * AJAX handler for generating JWT secret
     */
    public function ajax_generate_jwt_secret() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'smart_auth_admin_nonce')) {
            wp_die(__('Security check failed.', 'smart-auth'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions.', 'smart-auth'));
        }

        // Generate new secret
        $secret = wp_generate_password(64, true, true);

        wp_send_json_success(array(
            'secret' => $secret,
            'message' => __('New JWT secret generated successfully!', 'smart-auth'),
        ));
    }
}
