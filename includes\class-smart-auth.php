<?php
/**
 * Main Smart Auth Plugin Class
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Main Smart Auth class
 */
class Smart_Auth {
    
    /**
     * Plugin instance
     *
     * @var Smart_Auth
     */
    private static $instance = null;
    
    /**
     * Firebase Auth handler
     *
     * @var Smart_Auth_Firebase
     */
    public $firebase_auth;
    
    /**
     * Twilio Auth handler
     *
     * @var Smart_Auth_Twilio
     */
    public $twilio_auth;
    
    /**
     * JWT handler
     *
     * @var Smart_Auth_JWT_Handler
     */
    public $jwt_handler;
    
    /**
     * User sync handler
     *
     * @var Smart_Auth_User_Sync
     */
    public $user_sync;
    
    /**
     * Admin settings
     *
     * @var Smart_Auth_Admin_Settings
     */
    public $admin_settings;
    
    /**
     * Get plugin instance
     *
     * @return Smart_Auth
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize the plugin
     */
    private function init() {
        // Load dependencies
        $this->load_dependencies();
        
        // Initialize components
        $this->init_components();
        
        // Setup hooks
        $this->setup_hooks();
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        // Core classes
        require_once SMART_AUTH_PLUGIN_DIR . 'includes/class-firebase-auth.php';
        require_once SMART_AUTH_PLUGIN_DIR . 'includes/class-twilio-auth.php';
        require_once SMART_AUTH_PLUGIN_DIR . 'includes/class-jwt-handler.php';
        require_once SMART_AUTH_PLUGIN_DIR . 'includes/class-user-sync.php';
        
        // Admin classes
        require_once SMART_AUTH_PLUGIN_DIR . 'admin/class-admin-settings.php';
        
        // REST API classes
        require_once SMART_AUTH_PLUGIN_DIR . 'includes/rest-api/class-auth-endpoints.php';
        require_once SMART_AUTH_PLUGIN_DIR . 'includes/rest-api/class-user-endpoints.php';
        
        // Widget classes (load conditionally)
        if (did_action('elementor/loaded')) {
            require_once SMART_AUTH_PLUGIN_DIR . 'widgets/elementor/class-elementor-widgets.php';
        }
        
        if (class_exists('Bricks\Elements')) {
            require_once SMART_AUTH_PLUGIN_DIR . 'widgets/bricks/class-bricks-elements.php';
        }
    }
    
    /**
     * Initialize plugin components
     */
    private function init_components() {
        $this->firebase_auth = new Smart_Auth_Firebase();
        $this->twilio_auth = new Smart_Auth_Twilio();
        $this->jwt_handler = new Smart_Auth_JWT_Handler();
        $this->user_sync = new Smart_Auth_User_Sync();
        $this->admin_settings = new Smart_Auth_Admin_Settings();
        
        // Initialize REST API endpoints
        new Smart_Auth_Auth_Endpoints();
        new Smart_Auth_User_Endpoints();
        
        // Initialize widgets if page builders are active
        if (did_action('elementor/loaded')) {
            new Smart_Auth_Elementor_Widgets();
        }
        
        if (class_exists('Bricks\Elements')) {
            new Smart_Auth_Bricks_Elements();
        }
    }
    
    /**
     * Setup WordPress hooks
     */
    private function setup_hooks() {
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        
        // Register shortcodes
        add_action('init', array($this, 'register_shortcodes'));
        
        // AJAX handlers
        add_action('wp_ajax_smart_auth_firebase_verify', array($this, 'ajax_firebase_verify'));
        add_action('wp_ajax_nopriv_smart_auth_firebase_verify', array($this, 'ajax_firebase_verify'));
        add_action('wp_ajax_smart_auth_send_otp', array($this, 'ajax_send_otp'));
        add_action('wp_ajax_nopriv_smart_auth_send_otp', array($this, 'ajax_send_otp'));
        add_action('wp_ajax_smart_auth_verify_otp', array($this, 'ajax_verify_otp'));
        add_action('wp_ajax_nopriv_smart_auth_verify_otp', array($this, 'ajax_verify_otp'));
        
        // Login/logout hooks
        add_action('wp_login', array($this, 'on_user_login'), 10, 2);
        add_action('wp_logout', array($this, 'on_user_logout'));
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        wp_enqueue_script(
            'smart-auth-frontend',
            SMART_AUTH_PLUGIN_URL . 'assets/js/smart-auth.js',
            array('jquery'),
            SMART_AUTH_VERSION,
            true
        );
        
        wp_enqueue_style(
            'smart-auth-frontend',
            SMART_AUTH_PLUGIN_URL . 'assets/css/smart-auth.css',
            array(),
            SMART_AUTH_VERSION
        );
        
        // Localize script with settings
        wp_localize_script('smart-auth-frontend', 'smartAuth', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('smart_auth_nonce'),
            'firebaseConfig' => $this->get_firebase_config(),
            'strings' => array(
                'loading' => __('Loading...', 'smart-auth'),
                'error' => __('An error occurred. Please try again.', 'smart-auth'),
                'success' => __('Success!', 'smart-auth'),
                'invalidPhone' => __('Please enter a valid phone number.', 'smart-auth'),
                'otpSent' => __('OTP sent successfully.', 'smart-auth'),
                'otpInvalid' => __('Invalid OTP code.', 'smart-auth'),
            )
        ));
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on Smart Auth admin pages
        if (strpos($hook, 'smart-auth') === false) {
            return;
        }
        
        wp_enqueue_script(
            'smart-auth-admin',
            SMART_AUTH_PLUGIN_URL . 'admin/assets/js/admin.js',
            array('jquery'),
            SMART_AUTH_VERSION,
            true
        );
        
        wp_enqueue_style(
            'smart-auth-admin',
            SMART_AUTH_PLUGIN_URL . 'admin/assets/css/admin.css',
            array(),
            SMART_AUTH_VERSION
        );
    }
    
    /**
     * Register shortcodes
     */
    public function register_shortcodes() {
        add_shortcode('smart_auth_form', array($this, 'shortcode_auth_form'));
        add_shortcode('smart_auth_button', array($this, 'shortcode_auth_button'));
        add_shortcode('smart_auth_otp', array($this, 'shortcode_otp_form'));
    }
    
    /**
     * Get Firebase configuration for frontend
     */
    private function get_firebase_config() {
        $settings = get_option('smart_auth_firebase_settings', array());
        
        return array(
            'apiKey' => isset($settings['api_key']) ? $settings['api_key'] : '',
            'authDomain' => isset($settings['auth_domain']) ? $settings['auth_domain'] : '',
            'projectId' => isset($settings['project_id']) ? $settings['project_id'] : '',
            'storageBucket' => isset($settings['storage_bucket']) ? $settings['storage_bucket'] : '',
        );
    }
    
    /**
     * Plugin activation
     */
    public static function activate() {
        // Create default options
        add_option('smart_auth_version', SMART_AUTH_VERSION);
        add_option('smart_auth_firebase_settings', array());
        add_option('smart_auth_twilio_settings', array());
        add_option('smart_auth_jwt_settings', array(
            'secret_key' => wp_generate_password(64, true, true),
            'expiration' => 24 * HOUR_IN_SECONDS,
        ));
        add_option('smart_auth_user_settings', array(
            'auto_create_users' => true,
            'default_role' => 'subscriber',
            'sync_profile_picture' => true,
        ));
        add_option('smart_auth_security_settings', array(
            'rate_limit_attempts' => 3,
            'rate_limit_window' => 15 * MINUTE_IN_SECONDS,
            'otp_resend_cooldown' => 60,
        ));
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public static function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Remove options
        delete_option('smart_auth_version');
        delete_option('smart_auth_firebase_settings');
        delete_option('smart_auth_twilio_settings');
        delete_option('smart_auth_jwt_settings');
        delete_option('smart_auth_user_settings');
        delete_option('smart_auth_security_settings');
        
        // Remove user meta
        delete_metadata('user', 0, 'firebase_uid', '', true);
        delete_metadata('user', 0, 'auth_provider', '', true);
        delete_metadata('user', 0, 'phone_number', '', true);
        delete_metadata('user', 0, 'profile_picture_url', '', true);
        delete_metadata('user', 0, 'last_firebase_sync', '', true);
        
        // Clean up transients
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_smart_auth_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_smart_auth_%'");
    }
    
    // Placeholder methods for shortcodes and AJAX handlers
    public function shortcode_auth_form($atts) { return '<!-- Auth form shortcode -->'; }
    public function shortcode_auth_button($atts) { return '<!-- Auth button shortcode -->'; }
    public function shortcode_otp_form($atts) { return '<!-- OTP form shortcode -->'; }
    public function ajax_firebase_verify() { wp_die('Not implemented yet'); }
    public function ajax_send_otp() { wp_die('Not implemented yet'); }
    public function ajax_verify_otp() { wp_die('Not implemented yet'); }
    public function on_user_login($user_login, $user) { /* Handle user login */ }
    public function on_user_logout() { /* Handle user logout */ }
}
