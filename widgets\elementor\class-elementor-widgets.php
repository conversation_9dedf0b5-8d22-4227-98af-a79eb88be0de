<?php
/**
 * Elementor Widgets Handler
 *
 * @package SmartAuth
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Elementor Widgets class
 */
class Smart_Auth_Elementor_Widgets {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('elementor/widgets/register', array($this, 'register_widgets'));
        add_action('elementor/elements/categories_registered', array($this, 'add_widget_category'));
    }
    
    /**
     * Register widgets
     *
     * @param \Elementor\Widgets_Manager $widgets_manager Elementor widgets manager
     */
    public function register_widgets($widgets_manager) {
        // Load widget files
        require_once SMART_AUTH_PLUGIN_DIR . 'widgets/elementor/widgets/class-auth-form-widget.php';
        require_once SMART_AUTH_PLUGIN_DIR . 'widgets/elementor/widgets/class-social-login-widget.php';
        require_once SMART_AUTH_PLUGIN_DIR . 'widgets/elementor/widgets/class-phone-otp-widget.php';
        
        // Register widgets
        $widgets_manager->register(new Smart_Auth_Elementor_Auth_Form_Widget());
        $widgets_manager->register(new Smart_Auth_Elementor_Social_Login_Widget());
        $widgets_manager->register(new Smart_Auth_Elementor_Phone_OTP_Widget());
    }
    
    /**
     * Add widget category
     *
     * @param \Elementor\Elements_Manager $elements_manager Elementor elements manager
     */
    public function add_widget_category($elements_manager) {
        $elements_manager->add_category(
            'smart-auth',
            array(
                'title' => __('Smart Auth', 'smart-auth'),
                'icon' => 'fa fa-shield-alt',
            )
        );
    }
}
